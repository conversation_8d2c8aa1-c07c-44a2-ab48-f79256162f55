# دليل البدء السريع - TV Controller App

## 🚀 تثبيت سريع (5 دقائق)

### الخطوة 1: تحضير الجهاز
```bash
# فعّل خيارات المطور على التلفزيون
# اضغط على "رقم البناء" 7 مرات في الإعدادات
```

### الخطوة 2: بناء التطبيق
```bash
# على Windows
build_apk.bat

# على Linux/Mac
./gradlew assembleRelease
```

### الخطوة 3: التثبيت
```bash
# اتصل بالتلفزيون
adb connect [IP_ADDRESS]:5555

# ثبّت التطبيق
adb install app/build/outputs/apk/release/app-release.apk
```

### الخطوة 4: الإعداد
1. افتح التطبيق على التلفزيون
2. امنح الصلاحيات المطلوبة
3. اذه<PERSON> للإعدادات وأدخل IP الخادم
4. اضغ<PERSON> "تشغيل الخدمة"

## 🎯 اختبار سريع

### تشغيل خادم اختبار (Python)
```python
import socket
import time

server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
server.bind(('0.0.0.0', 8080))
server.listen(1)
print("خادم الاختبار يعمل...")

while True:
    client, addr = server.accept()
    print(f"اتصال من {addr}")
    
    # إرسال أمر عرض الشاشة السوداء
    client.send(b"SHOW_BLACK_SCREEN")
    time.sleep(5)
    
    # إرسال أمر إخفاء الشاشة
    client.send(b"HIDE_BLACK_SCREEN")
    
    client.close()
```

### تشغيل الخادم
```bash
python test_server.py
```

## 📱 أوامر سريعة

| الأمر | الوصف |
|-------|--------|
| `SHOW_BLACK_SCREEN` | عرض الشاشة السوداء |
| `HIDE_BLACK_SCREEN` | إخفاء الشاشة السوداء |
| `RESTART_APP` | إعادة تشغيل التطبيق |
| `MESSAGE:النص` | عرض رسالة مخصصة |
| `COUNTDOWN:30` | عد تنازلي 30 ثانية |

## 🔧 استكشاف الأخطاء السريع

### المشكلة: التطبيق لا يثبت
```bash
# تأكد من تمكين المصادر غير المعروفة
adb shell settings put global install_non_market_apps 1
```

### المشكلة: الشاشة السوداء لا تظهر
```bash
# تحقق من الصلاحيات
adb shell appops set com.tvcontroller.app SYSTEM_ALERT_WINDOW allow
```

### المشكلة: الخدمة تتوقف
```bash
# إيقاف تحسين البطارية
adb shell dumpsys deviceidle whitelist +com.tvcontroller.app
```

## 📊 مراقبة التطبيق

### عرض السجلات
```bash
adb logcat | grep TVController
```

### حالة التطبيق
```bash
adb shell am force-stop com.tvcontroller.app
adb shell am start -n com.tvcontroller.app/.MainActivity
```

### معلومات الذاكرة
```bash
adb shell dumpsys meminfo com.tvcontroller.app
```

## 🎮 أوامر التحكم عن بُعد

### إرسال أمر عبر ADB
```bash
adb shell am broadcast -a com.tvcontroller.app.COMMAND --es command "SHOW_BLACK_SCREEN"
```

### إرسال أمر عبر HTTP
```bash
curl -X POST http://TV_IP:8080/command -d "SHOW_BLACK_SCREEN"
```

## 📋 قائمة مراجعة سريعة

- [ ] تم تفعيل خيارات المطور
- [ ] تم تمكين تصحيح USB
- [ ] تم بناء APK بنجاح
- [ ] تم تثبيت التطبيق
- [ ] تم منح صلاحية العرض فوق التطبيقات
- [ ] تم إيقاف تحسين البطارية
- [ ] تم إعداد IP الخادم
- [ ] تم تشغيل الخدمة
- [ ] تم اختبار الاتصال

## 🆘 الدعم السريع

### مشاكل شائعة وحلولها

**التطبيق يتوقف عند التشغيل:**
```bash
adb logcat | grep AndroidRuntime
```

**لا يمكن الاتصال بالخادم:**
```bash
adb shell ping [SERVER_IP]
```

**الشاشة السوداء تختفي:**
```bash
adb shell settings put global policy_control immersive.full=com.tvcontroller.app
```

---
💡 **نصيحة**: احفظ هذا الملف كمرجع سريع أثناء التطوير والاختبار!
