{"logs": [{"outputFile": "com.tvcontroller.app-mergeDebugResources-45:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8944fc038c0e2d5da876e70d3ee0f81b\\transformed\\preference-1.2.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,182,266,349,492,661,752", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "177,261,344,487,656,747,827"}, "to": {"startLines": "48,50,109,111,114,115,116", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4526,4668,9216,9380,9707,9876,9967", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "4598,4747,9294,9518,9871,9962,10042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\566c99b1f2c65122017c8029b64a207c\\transformed\\appcompat-1.6.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,430,529,636,727,832,952,1029,1104,1195,1288,1383,1477,1577,1670,1765,1859,1950,2041,2127,2240,2348,2451,2560,2676,2796,2963,9523", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "425,524,631,722,827,947,1024,1099,1190,1283,1378,1472,1572,1665,1760,1854,1945,2036,2122,2235,2343,2446,2555,2671,2791,2958,3060,9601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ba6274a1892fdd5def28bdd9767110ee\\transformed\\core-1.12.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "38,39,40,41,42,43,44,113", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3573,3676,3784,3889,3993,4093,9606", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3568,3671,3779,3884,3988,4088,4217,9702"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4dde4ede0bc8a81ca43b3c1837b4aed4\\transformed\\material-1.11.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,781,904,983,1048,1137,1202,1261,1347,1411,1475,1538,1608,1672,1726,1831,1889,1951,2005,2077,2194,2281,2364,2504,2581,2662,2789,2880,2957,3011,3062,3128,3198,3275,3362,3437,3508,3585,3654,3723,3830,3921,3993,4082,4171,4245,4317,4403,4453,4532,4598,4678,4762,4824,4888,4951,5020,5120,5215,5307,5399,5457,5512", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,77,76,85,83,101,122,78,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80", "endOffsets": "267,349,427,504,590,674,776,899,978,1043,1132,1197,1256,1342,1406,1470,1533,1603,1667,1721,1826,1884,1946,2000,2072,2189,2276,2359,2499,2576,2657,2784,2875,2952,3006,3057,3123,3193,3270,3357,3432,3503,3580,3649,3718,3825,3916,3988,4077,4166,4240,4312,4398,4448,4527,4593,4673,4757,4819,4883,4946,5015,5115,5210,5302,5394,5452,5507,5588"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3065,3147,3225,3302,3388,4222,4324,4447,4603,4752,4841,4906,4965,5051,5115,5179,5242,5312,5376,5430,5535,5593,5655,5709,5781,5898,5985,6068,6208,6285,6366,6493,6584,6661,6715,6766,6832,6902,6979,7066,7141,7212,7289,7358,7427,7534,7625,7697,7786,7875,7949,8021,8107,8157,8236,8302,8382,8466,8528,8592,8655,8724,8824,8919,9011,9103,9161,9299", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,110", "endColumns": "12,81,77,76,85,83,101,122,78,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80", "endOffsets": "317,3142,3220,3297,3383,3467,4319,4442,4521,4663,4836,4901,4960,5046,5110,5174,5237,5307,5371,5425,5530,5588,5650,5704,5776,5893,5980,6063,6203,6280,6361,6488,6579,6656,6710,6761,6827,6897,6974,7061,7136,7207,7284,7353,7422,7529,7620,7692,7781,7870,7944,8016,8102,8152,8231,8297,8377,8461,8523,8587,8650,8719,8819,8914,9006,9098,9156,9211,9375"}}]}]}