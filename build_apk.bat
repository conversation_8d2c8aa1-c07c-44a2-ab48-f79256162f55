@echo off
echo Building TV Controller APK...
echo.

echo Step 1: Creating keystore (if not exists)...
if not exist "app\release-key.keystore" (
    echo Creating keystore...
    keytool -genkey -v -keystore app\release-key.keystore -alias tvcontroller -keyalg RSA -keysize 2048 -validity 10000 -storepass tvcontroller123 -keypass tvcontroller123 -dname "CN=TV Controller, OU=Development, O=TV Controller App, L=City, S=State, C=US"
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to create keystore. Please install Java JDK and ensure keytool is in PATH.
        pause
        exit /b 1
    )
    echo Keystore created successfully!
) else (
    echo Keystore already exists.
)

echo.
echo Step 2: Cleaning project...
call gradlew clean
if %ERRORLEVEL% NEQ 0 (
    echo Clean failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Building debug APK...
call gradlew assembleDebug
if %ERRORLEVEL% NEQ 0 (
    echo Debug build failed!
    pause
    exit /b 1
)

echo.
echo Step 4: Building release APK...
call gradlew assembleRelease
if %ERRORLEVEL% NEQ 0 (
    echo Release build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo.
echo APK files created:
echo - Debug APK: app\build\outputs\apk\debug\app-debug.apk
echo - Release APK: app\build\outputs\apk\release\app-release.apk
echo.
echo You can install the APK on your Android TV using:
echo 1. ADB: adb install app-release.apk
echo 2. USB transfer and file manager
echo 3. Network transfer (if supported)
echo.

pause
