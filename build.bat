@echo off
echo ========================================
echo     TV Controller - Build APK
echo ========================================
echo.

echo Checking Java...
java -version
if %errorlevel% NEQ 0 (
    echo ERROR: Java not found!
    echo Please install Java JDK
    pause
    exit /b 1
)
echo Java OK!
echo.

echo Creating keystore if needed...
if not exist "app\release-key.keystore" (
    echo Creating keystore...
    keytool -genkey -v -keystore app\release-key.keystore -alias tvcontroller -keyalg RSA -keysize 2048 -validity 10000 -storepass tvcontroller123 -keypass tvcontroller123 -dname "CN=TV Controller, OU=Development, O=TV Controller App, L=City, S=State, C=US"
    if %errorlevel% NEQ 0 (
        echo ERROR: Failed to create keystore
        pause
        exit /b 1
    )
    echo Keystore created!
) else (
    echo Keystore exists!
)
echo.

echo Cleaning project...
call gradlew clean
if %errorlevel% NEQ 0 (
    echo ERROR: Clean failed
    pause
    exit /b 1
)
echo.

echo Building Debug APK...
call gradlew assembleDebug
if %errorlevel% NEQ 0 (
    echo ERROR: Debug build failed
    pause
    exit /b 1
)
echo.

echo Building Release APK...
call gradlew assembleRelease
if %errorlevel% NEQ 0 (
    echo ERROR: Release build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo           BUILD COMPLETE!
echo ========================================
echo.
echo APK files created:
echo.
echo Debug APK:
echo app\build\outputs\apk\debug\app-debug.apk
echo.
echo Release APK:
echo app\build\outputs\apk\release\app-release.apk
echo.
echo To install on TV:
echo adb connect [TV_IP]:5555
echo adb install app\build\outputs\apk\release\app-release.apk
echo.
pause
