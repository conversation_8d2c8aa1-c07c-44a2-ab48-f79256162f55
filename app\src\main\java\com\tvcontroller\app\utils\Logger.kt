package com.tvcontroller.app.utils

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*

object Logger {
    
    private const val TAG = "TVController"
    private const val LOG_FILE_NAME = "tvcontroller.log"
    private const val MAX_LOG_SIZE = 5 * 1024 * 1024 // 5MB
    private const val MAX_LOG_FILES = 3
    
    private var context: Context? = null
    private var logToFile = true
    private var logLevel = Log.DEBUG
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    fun init(context: Context, enableFileLogging: Boolean = true, level: Int = Log.DEBUG) {
        this.context = context
        this.logToFile = enableFileLogging
        this.logLevel = level
        
        if (enableFileLogging) {
            cleanOldLogs()
        }
        
        i("Logger initialized - File logging: $enableFileLogging, Level: ${getLevelName(level)}")
    }
    
    fun v(message: String, tag: String = TAG) {
        if (logLevel <= Log.VERBOSE) {
            Log.v(tag, message)
            writeToFile("V", tag, message)
        }
    }
    
    fun d(message: String, tag: String = TAG) {
        if (logLevel <= Log.DEBUG) {
            Log.d(tag, message)
            writeToFile("D", tag, message)
        }
    }
    
    fun i(message: String, tag: String = TAG) {
        if (logLevel <= Log.INFO) {
            Log.i(tag, message)
            writeToFile("I", tag, message)
        }
    }
    
    fun w(message: String, tag: String = TAG) {
        if (logLevel <= Log.WARN) {
            Log.w(tag, message)
            writeToFile("W", tag, message)
        }
    }
    
    fun e(message: String, throwable: Throwable? = null, tag: String = TAG) {
        if (logLevel <= Log.ERROR) {
            if (throwable != null) {
                Log.e(tag, message, throwable)
                writeToFile("E", tag, "$message\n${Log.getStackTraceString(throwable)}")
            } else {
                Log.e(tag, message)
                writeToFile("E", tag, message)
            }
        }
    }
    
    fun wtf(message: String, throwable: Throwable? = null, tag: String = TAG) {
        Log.wtf(tag, message, throwable)
        val fullMessage = if (throwable != null) {
            "$message\n${Log.getStackTraceString(throwable)}"
        } else {
            message
        }
        writeToFile("WTF", tag, fullMessage)
    }
    
    // Specialized logging methods for different components
    fun service(message: String) = i("SERVICE: $message", "Service")
    fun connection(message: String) = i("CONNECTION: $message", "Connection")
    fun overlay(message: String) = i("OVERLAY: $message", "Overlay")
    fun receiver(message: String) = i("RECEIVER: $message", "Receiver")
    fun worker(message: String) = i("WORKER: $message", "Worker")
    fun network(message: String) = i("NETWORK: $message", "Network")
    fun permission(message: String) = i("PERMISSION: $message", "Permission")
    
    // Error logging methods
    fun serviceError(message: String, throwable: Throwable? = null) = e("SERVICE ERROR: $message", throwable, "Service")
    fun connectionError(message: String, throwable: Throwable? = null) = e("CONNECTION ERROR: $message", throwable, "Connection")
    fun overlayError(message: String, throwable: Throwable? = null) = e("OVERLAY ERROR: $message", throwable, "Overlay")
    fun networkError(message: String, throwable: Throwable? = null) = e("NETWORK ERROR: $message", throwable, "Network")
    
    private fun writeToFile(level: String, tag: String, message: String) {
        if (!logToFile || context == null) return
        
        try {
            val logDir = File(context!!.filesDir, "logs")
            if (!logDir.exists()) {
                logDir.mkdirs()
            }
            
            val logFile = File(logDir, LOG_FILE_NAME)
            
            // Check file size and rotate if necessary
            if (logFile.exists() && logFile.length() > MAX_LOG_SIZE) {
                rotateLogFiles(logDir)
            }
            
            val timestamp = dateFormat.format(Date())
            val logEntry = "$timestamp $level/$tag: $message\n"
            
            FileWriter(logFile, true).use { writer ->
                writer.append(logEntry)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write to log file", e)
        }
    }
    
    private fun rotateLogFiles(logDir: File) {
        try {
            // Move existing log files
            for (i in MAX_LOG_FILES - 1 downTo 1) {
                val oldFile = File(logDir, "$LOG_FILE_NAME.$i")
                val newFile = File(logDir, "$LOG_FILE_NAME.${i + 1}")
                
                if (oldFile.exists()) {
                    if (i == MAX_LOG_FILES - 1) {
                        oldFile.delete() // Delete oldest file
                    } else {
                        oldFile.renameTo(newFile)
                    }
                }
            }
            
            // Move current log file
            val currentFile = File(logDir, LOG_FILE_NAME)
            val backupFile = File(logDir, "$LOG_FILE_NAME.1")
            currentFile.renameTo(backupFile)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to rotate log files", e)
        }
    }
    
    private fun cleanOldLogs() {
        try {
            val logDir = File(context!!.filesDir, "logs")
            if (!logDir.exists()) return
            
            val logFiles = logDir.listFiles { file ->
                file.name.startsWith(LOG_FILE_NAME)
            }
            
            logFiles?.sortedByDescending { it.lastModified() }
                ?.drop(MAX_LOG_FILES)
                ?.forEach { it.delete() }
                
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clean old logs", e)
        }
    }
    
    fun getLogFiles(): List<File> {
        val logDir = File(context!!.filesDir, "logs")
        if (!logDir.exists()) return emptyList()
        
        return logDir.listFiles { file ->
            file.name.startsWith(LOG_FILE_NAME)
        }?.sortedByDescending { it.lastModified() }?.toList() ?: emptyList()
    }
    
    fun getLogContent(maxLines: Int = 1000): String {
        return try {
            val logFile = File(context!!.filesDir, "logs/$LOG_FILE_NAME")
            if (!logFile.exists()) return "No log file found"
            
            val lines = logFile.readLines()
            lines.takeLast(maxLines).joinToString("\n")
        } catch (e: Exception) {
            "Error reading log file: ${e.message}"
        }
    }
    
    fun clearLogs() {
        try {
            val logDir = File(context!!.filesDir, "logs")
            if (logDir.exists()) {
                logDir.listFiles()?.forEach { it.delete() }
            }
            i("Log files cleared")
        } catch (e: Exception) {
            e("Failed to clear log files", e)
        }
    }
    
    fun exportLogs(): String? {
        return try {
            val logFile = File(context!!.filesDir, "logs/$LOG_FILE_NAME")
            if (logFile.exists()) {
                logFile.absolutePath
            } else {
                null
            }
        } catch (e: Exception) {
            e("Failed to export logs", e)
            null
        }
    }
    
    private fun getLevelName(level: Int): String {
        return when (level) {
            Log.VERBOSE -> "VERBOSE"
            Log.DEBUG -> "DEBUG"
            Log.INFO -> "INFO"
            Log.WARN -> "WARN"
            Log.ERROR -> "ERROR"
            Log.ASSERT -> "ASSERT"
            else -> "UNKNOWN"
        }
    }
    
    fun getLogStats(): String {
        return try {
            val logFiles = getLogFiles()
            val totalSize = logFiles.sumOf { it.length() }
            val totalLines = logFiles.sumOf { file ->
                try {
                    file.readLines().size
                } catch (e: Exception) {
                    0
                }
            }
            
            buildString {
                append("Log Statistics:\n")
                append("Files: ${logFiles.size}\n")
                append("Total Size: ${totalSize / 1024} KB\n")
                append("Total Lines: $totalLines\n")
                append("File Logging: ${if (logToFile) "Enabled" else "Disabled"}\n")
                append("Log Level: ${getLevelName(logLevel)}\n")
            }
        } catch (e: Exception) {
            "Error getting log stats: ${e.message}"
        }
    }
}
