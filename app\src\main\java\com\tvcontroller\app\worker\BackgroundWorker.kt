package com.tvcontroller.app.worker

import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.work.*
import com.tvcontroller.app.service.OverlayService
import com.tvcontroller.app.utils.ServiceHelper
import java.util.concurrent.TimeUnit

class BackgroundWorker(
    context: Context,
    workerParams: WorkerParameters
) : Worker(context, workerParams) {
    
    override fun doWork(): Result {
        Log.d(TAG, "BackgroundWorker executing...")

        return try {
            // Check if overlay service is running
            val isServiceRunning = ServiceHelper.isServiceRunning(applicationContext, OverlayService::class.java)

            if (!isServiceRunning) {
                Log.d(TAG, "OverlayService not running, starting it...")

                // Start the overlay service
                val serviceIntent = Intent(applicationContext, OverlayService::class.java)

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    applicationContext.startForegroundService(serviceIntent)
                } else {
                    applicationContext.startService(serviceIntent)
                }

                Log.d(TAG, "OverlayService started successfully")
            } else {
                Log.d(TAG, "OverlayService is already running")
            }

            // Check memory usage and log system info
            val systemInfo = ServiceHelper.getSystemInfo(applicationContext)
            Log.d(TAG, "System Info:\n$systemInfo")

            // Check if device has low memory
            if (ServiceHelper.isLowMemory(applicationContext)) {
                Log.w(TAG, "Device has low memory, service might be killed")
            }

            Result.success()

        } catch (e: Exception) {
            Log.e(TAG, "Error in BackgroundWorker: ${e.message}")
            Result.retry()
        }
    }

    companion object {
        private const val TAG = "BackgroundWorker"
        private const val WORK_NAME = "service_monitor_work"
        private const val REPEAT_INTERVAL = 15L // 15 minutes

        /**
         * Schedule periodic work to monitor and restart service
         */
        fun schedulePeriodicWork(context: Context) {
            Log.d(TAG, "Scheduling periodic work")

            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .setRequiresBatteryNotLow(false)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .setRequiresStorageNotLow(false)
                .build()

            val workRequest = PeriodicWorkRequestBuilder<BackgroundWorker>(
                REPEAT_INTERVAL, TimeUnit.MINUTES,
                5, TimeUnit.MINUTES // Flex interval
            )
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.LINEAR,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()

            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WORK_NAME,
                ExistingPeriodicWorkPolicy.REPLACE,
                workRequest
            )

            Log.d(TAG, "Periodic work scheduled successfully")
        }

        fun cancelPeriodicWork(context: Context) {
            Log.d(TAG, "Cancelling periodic work")
            WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME)
        }

        fun getWorkStatus(context: Context): androidx.lifecycle.LiveData<List<WorkInfo>> {
            return WorkManager.getInstance(context).getWorkInfosForUniqueWorkLiveData(WORK_NAME)
        }
    }
}

/**
 * One-time worker for immediate service restart
 */
class ServiceRestartWorker(
    context: Context,
    workerParams: WorkerParameters
) : Worker(context, workerParams) {

    override fun doWork(): Result {
        Log.d(TAG, "ServiceRestartWorker executing...")

        return try {
            // Stop existing service first
            val stopIntent = Intent(applicationContext, OverlayService::class.java)
            applicationContext.stopService(stopIntent)

            // Wait a moment
            Thread.sleep(2000)

            // Start service again
            val startIntent = Intent(applicationContext, OverlayService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                applicationContext.startForegroundService(startIntent)
            } else {
                applicationContext.startService(startIntent)
            }

            Log.d(TAG, "Service restarted successfully")
            Result.success()

        } catch (e: Exception) {
            Log.e(TAG, "Error restarting service: ${e.message}")
            Result.failure()
        }
    }

    companion object {
        private const val TAG = "ServiceRestartWorker"

        fun scheduleServiceRestart(context: Context, delaySeconds: Long = 0) {
            Log.d(TAG, "Scheduling service restart in $delaySeconds seconds")

            val workRequest = OneTimeWorkRequestBuilder<ServiceRestartWorker>()
                .setInitialDelay(delaySeconds, TimeUnit.SECONDS)
                .build()

            WorkManager.getInstance(context).enqueue(workRequest)
        }
    }
}
