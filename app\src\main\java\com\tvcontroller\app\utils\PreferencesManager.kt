package com.tvcontroller.app.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.preference.PreferenceManager

class PreferencesManager(context: Context) {
    
    private val sharedPreferences: SharedPreferences = PreferenceManager.getDefaultSharedPreferences(context)
    
    companion object {
        // Server settings
        private const val KEY_SERVER_IP = "server_ip"
        private const val KEY_SERVER_PORT = "server_port"
        private const val KEY_CONNECTION_TYPE = "connection_type"
        private const val KEY_RECONNECT_ATTEMPTS = "reconnect_attempts"
        private const val KEY_RECONNECT_DELAY = "reconnect_delay"
        
        // App settings
        private const val KEY_AUTO_START = "auto_start"
        private const val KEY_LANGUAGE = "language"
        private const val KEY_THEME = "theme"
        private const val KEY_KEEP_SCREEN_ON = "keep_screen_on"
        
        // Overlay settings
        private const val KEY_OVERLAY_MESSAGE = "overlay_message"
        private const val KEY_OVERLAY_TIMEOUT = "overlay_timeout"
        private const val KEY_SHOW_COUNTDOWN = "show_countdown"
        private const val KEY_OVERLAY_OPACITY = "overlay_opacity"
        
        // Service settings
        private const val KEY_SERVICE_ENABLED = "service_enabled"
        private const val KEY_NOTIFICATION_ENABLED = "notification_enabled"
        private const val KEY_VIBRATION_ENABLED = "vibration_enabled"
        
        // Statistics
        private const val KEY_LAST_CONNECTION_TIME = "last_connection_time"
        private const val KEY_TOTAL_CONNECTIONS = "total_connections"
        private const val KEY_TOTAL_COMMANDS_RECEIVED = "total_commands_received"
        
        // Default values
        private const val DEFAULT_SERVER_IP = "*************"
        private const val DEFAULT_SERVER_PORT = 8080
        private const val DEFAULT_CONNECTION_TYPE = "websocket"
        private const val DEFAULT_RECONNECT_ATTEMPTS = 10
        private const val DEFAULT_RECONNECT_DELAY = 5000L
        private const val DEFAULT_LANGUAGE = "ar"
        private const val DEFAULT_OVERLAY_MESSAGE = "الشاشة مقفلة - Screen Locked"
        private const val DEFAULT_OVERLAY_TIMEOUT = 0 // 0 = no timeout
        private const val DEFAULT_OVERLAY_OPACITY = 100
    }
    
    // Server settings
    var serverIp: String
        get() = sharedPreferences.getString(KEY_SERVER_IP, DEFAULT_SERVER_IP) ?: DEFAULT_SERVER_IP
        set(value) = sharedPreferences.edit().putString(KEY_SERVER_IP, value).apply()
    
    var serverPort: Int
        get() = sharedPreferences.getInt(KEY_SERVER_PORT, DEFAULT_SERVER_PORT)
        set(value) = sharedPreferences.edit().putInt(KEY_SERVER_PORT, value).apply()
    
    var connectionType: String
        get() = sharedPreferences.getString(KEY_CONNECTION_TYPE, DEFAULT_CONNECTION_TYPE) ?: DEFAULT_CONNECTION_TYPE
        set(value) = sharedPreferences.edit().putString(KEY_CONNECTION_TYPE, value).apply()
    
    var reconnectAttempts: Int
        get() = sharedPreferences.getInt(KEY_RECONNECT_ATTEMPTS, DEFAULT_RECONNECT_ATTEMPTS)
        set(value) = sharedPreferences.edit().putInt(KEY_RECONNECT_ATTEMPTS, value).apply()
    
    var reconnectDelay: Long
        get() = sharedPreferences.getLong(KEY_RECONNECT_DELAY, DEFAULT_RECONNECT_DELAY)
        set(value) = sharedPreferences.edit().putLong(KEY_RECONNECT_DELAY, value).apply()
    
    // App settings
    var autoStart: Boolean
        get() = sharedPreferences.getBoolean(KEY_AUTO_START, true)
        set(value) = sharedPreferences.edit().putBoolean(KEY_AUTO_START, value).apply()
    
    var language: String
        get() = sharedPreferences.getString(KEY_LANGUAGE, DEFAULT_LANGUAGE) ?: DEFAULT_LANGUAGE
        set(value) = sharedPreferences.edit().putString(KEY_LANGUAGE, value).apply()
    
    var theme: String
        get() = sharedPreferences.getString(KEY_THEME, "dark") ?: "dark"
        set(value) = sharedPreferences.edit().putString(KEY_THEME, value).apply()
    
    var keepScreenOn: Boolean
        get() = sharedPreferences.getBoolean(KEY_KEEP_SCREEN_ON, true)
        set(value) = sharedPreferences.edit().putBoolean(KEY_KEEP_SCREEN_ON, value).apply()
    
    // Overlay settings
    var overlayMessage: String
        get() = sharedPreferences.getString(KEY_OVERLAY_MESSAGE, DEFAULT_OVERLAY_MESSAGE) ?: DEFAULT_OVERLAY_MESSAGE
        set(value) = sharedPreferences.edit().putString(KEY_OVERLAY_MESSAGE, value).apply()
    
    var overlayTimeout: Int
        get() = sharedPreferences.getInt(KEY_OVERLAY_TIMEOUT, DEFAULT_OVERLAY_TIMEOUT)
        set(value) = sharedPreferences.edit().putInt(KEY_OVERLAY_TIMEOUT, value).apply()
    
    var showCountdown: Boolean
        get() = sharedPreferences.getBoolean(KEY_SHOW_COUNTDOWN, true)
        set(value) = sharedPreferences.edit().putBoolean(KEY_SHOW_COUNTDOWN, value).apply()
    
    var overlayOpacity: Int
        get() = sharedPreferences.getInt(KEY_OVERLAY_OPACITY, DEFAULT_OVERLAY_OPACITY)
        set(value) = sharedPreferences.edit().putInt(KEY_OVERLAY_OPACITY, value).apply()
    
    // Service settings
    var serviceEnabled: Boolean
        get() = sharedPreferences.getBoolean(KEY_SERVICE_ENABLED, false)
        set(value) = sharedPreferences.edit().putBoolean(KEY_SERVICE_ENABLED, value).apply()
    
    var notificationEnabled: Boolean
        get() = sharedPreferences.getBoolean(KEY_NOTIFICATION_ENABLED, true)
        set(value) = sharedPreferences.edit().putBoolean(KEY_NOTIFICATION_ENABLED, value).apply()
    
    var vibrationEnabled: Boolean
        get() = sharedPreferences.getBoolean(KEY_VIBRATION_ENABLED, false)
        set(value) = sharedPreferences.edit().putBoolean(KEY_VIBRATION_ENABLED, value).apply()
    
    // Statistics
    var lastConnectionTime: Long
        get() = sharedPreferences.getLong(KEY_LAST_CONNECTION_TIME, 0)
        set(value) = sharedPreferences.edit().putLong(KEY_LAST_CONNECTION_TIME, value).apply()
    
    var totalConnections: Int
        get() = sharedPreferences.getInt(KEY_TOTAL_CONNECTIONS, 0)
        set(value) = sharedPreferences.edit().putInt(KEY_TOTAL_CONNECTIONS, value).apply()
    
    var totalCommandsReceived: Int
        get() = sharedPreferences.getInt(KEY_TOTAL_COMMANDS_RECEIVED, 0)
        set(value) = sharedPreferences.edit().putInt(KEY_TOTAL_COMMANDS_RECEIVED, value).apply()
    
    // Helper methods
    fun incrementConnections() {
        totalConnections++
        lastConnectionTime = System.currentTimeMillis()
    }
    
    fun incrementCommandsReceived() {
        totalCommandsReceived++
    }
    
    fun resetStatistics() {
        totalConnections = 0
        totalCommandsReceived = 0
        lastConnectionTime = 0
    }
    
    fun resetToDefaults() {
        sharedPreferences.edit().clear().apply()
    }
    
    fun exportSettings(): Map<String, Any?> {
        return sharedPreferences.all
    }
    
    fun importSettings(settings: Map<String, Any?>) {
        val editor = sharedPreferences.edit()
        settings.forEach { (key, value) ->
            when (value) {
                is String -> editor.putString(key, value)
                is Int -> editor.putInt(key, value)
                is Long -> editor.putLong(key, value)
                is Boolean -> editor.putBoolean(key, value)
                is Float -> editor.putFloat(key, value)
            }
        }
        editor.apply()
    }
    
    fun getSettingsSummary(): String {
        return buildString {
            append("=== إعدادات الخادم ===\n")
            append("IP: $serverIp\n")
            append("Port: $serverPort\n")
            append("Connection Type: $connectionType\n")
            append("Reconnect Attempts: $reconnectAttempts\n")
            append("Reconnect Delay: ${reconnectDelay}ms\n")
            append("\n=== إعدادات التطبيق ===\n")
            append("Auto Start: $autoStart\n")
            append("Language: $language\n")
            append("Theme: $theme\n")
            append("Keep Screen On: $keepScreenOn\n")
            append("\n=== إعدادات الشاشة ===\n")
            append("Overlay Message: $overlayMessage\n")
            append("Overlay Timeout: ${if (overlayTimeout == 0) "No timeout" else "${overlayTimeout}s"}\n")
            append("Show Countdown: $showCountdown\n")
            append("Overlay Opacity: $overlayOpacity%\n")
            append("\n=== إحصائيات ===\n")
            append("Total Connections: $totalConnections\n")
            append("Commands Received: $totalCommandsReceived\n")
            if (lastConnectionTime > 0) {
                append("Last Connection: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(lastConnectionTime))}\n")
            }
        }
    }
}
