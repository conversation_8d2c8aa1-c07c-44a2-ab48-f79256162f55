#Sat Jul 05 08:25:14 AST 2025
com.tvcontroller.app-main-51\:/drawable/ic_settings.xml=C\:\\shasha\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_settings.xml.flat
com.tvcontroller.app-mergeDebugResources-48\:/layout/activity_main.xml=C\:\\shasha\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.tvcontroller.app-main-51\:/xml/backup_rules.xml=C\:\\shasha\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.tvcontroller.app-mergeDebugResources-48\:/layout/overlay_layout.xml=C\:\\shasha\\app\\build\\intermediates\\merged_res\\debug\\layout_overlay_layout.xml.flat
com.tvcontroller.app-mergeDebugResources-48\:/layout/activity_settings.xml=C\:\\shasha\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_settings.xml.flat
com.tvcontroller.app-main-51\:/drawable/animated_circle.xml=C\:\\shasha\\app\\build\\intermediates\\merged_res\\debug\\drawable_animated_circle.xml.flat
com.tvcontroller.app-main-51\:/drawable/ic_lock_screen.xml=C\:\\shasha\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_lock_screen.xml.flat
com.tvcontroller.app-main-51\:/drawable/ic_tv_control.xml=C\:\\shasha\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_tv_control.xml.flat
com.tvcontroller.app-main-51\:/mipmap-hdpi/ic_launcher.png=C\:\\shasha\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.png.flat
com.tvcontroller.app-main-51\:/xml/data_extraction_rules.xml=C\:\\shasha\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
