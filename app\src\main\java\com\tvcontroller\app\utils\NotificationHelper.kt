package com.tvcontroller.app.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import com.tvcontroller.app.MainActivity
import com.tvcontroller.app.R

object NotificationHelper {
    
    private const val CHANNEL_ID_SERVICE = "tv_controller_service"
    private const val CHANNEL_ID_ALERTS = "tv_controller_alerts"
    private const val CHANNEL_ID_COMMANDS = "tv_controller_commands"
    
    private const val NOTIFICATION_ID_SERVICE = 1001
    private const val NOTIFICATION_ID_CONNECTION = 1002
    private const val NOTIFICATION_ID_COMMAND = 1003
    
    fun createNotificationChannels(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // Service channel
            val serviceChannel = NotificationChannel(
                CHANNEL_ID_SERVICE,
                "TV Controller Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Notifications for TV Controller background service"
                setShowBadge(false)
                enableVibration(false)
                enableLights(false)
            }
            
            // Alerts channel
            val alertsChannel = NotificationChannel(
                CHANNEL_ID_ALERTS,
                "Connection Alerts",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Notifications for connection status and alerts"
                setShowBadge(true)
                enableVibration(true)
                enableLights(true)
            }
            
            // Commands channel
            val commandsChannel = NotificationChannel(
                CHANNEL_ID_COMMANDS,
                "Command Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for received commands"
                setShowBadge(true)
                enableVibration(true)
                enableLights(true)
            }
            
            notificationManager.createNotificationChannels(listOf(
                serviceChannel,
                alertsChannel,
                commandsChannel
            ))
        }
    }
    
    fun createServiceNotification(context: Context): android.app.Notification {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(context, CHANNEL_ID_SERVICE)
            .setContentTitle(context.getString(R.string.service_running))
            .setContentText(context.getString(R.string.service_description))
            .setSmallIcon(R.drawable.ic_tv_control)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }
    
    fun showConnectionNotification(context: Context, isConnected: Boolean, serverInfo: String) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        val title = if (isConnected) "متصل بالخادم" else "انقطع الاتصال"
        val text = if (isConnected) "متصل بـ $serverInfo" else "فقد الاتصال مع $serverInfo"
        val icon = if (isConnected) R.drawable.ic_tv_control else R.drawable.ic_lock_screen
        
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_ALERTS)
            .setContentTitle(title)
            .setContentText(text)
            .setSmallIcon(icon)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_STATUS)
            .build()
        
        notificationManager.notify(NOTIFICATION_ID_CONNECTION, notification)
    }
    
    fun showCommandNotification(context: Context, command: String, details: String? = null) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        val title = when (command.uppercase()) {
            "SHOW_BLACK_SCREEN" -> "عرض الشاشة السوداء"
            "HIDE_BLACK_SCREEN" -> "إخفاء الشاشة السوداء"
            "RESTART_APP" -> "إعادة تشغيل التطبيق"
            else -> "أمر جديد: $command"
        }
        
        val text = details ?: "تم تنفيذ الأمر بنجاح"
        
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_COMMANDS)
            .setContentTitle(title)
            .setContentText(text)
            .setSmallIcon(R.drawable.ic_tv_control)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .build()
        
        notificationManager.notify(NOTIFICATION_ID_COMMAND, notification)
    }
    
    fun showErrorNotification(context: Context, error: String, details: String? = null) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_ALERTS)
            .setContentTitle("خطأ في التطبيق")
            .setContentText(error)
            .setStyle(NotificationCompat.BigTextStyle().bigText(details ?: error))
            .setSmallIcon(R.drawable.ic_lock_screen)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_ERROR)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .build()
        
        notificationManager.notify(System.currentTimeMillis().toInt(), notification)
    }
    
    fun showPermissionNotification(context: Context, permissionType: String) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        val title = when (permissionType) {
            "overlay" -> "صلاحية العرض مطلوبة"
            "battery" -> "إيقاف تحسين البطارية"
            else -> "صلاحية مطلوبة"
        }
        
        val text = when (permissionType) {
            "overlay" -> "يحتاج التطبيق لصلاحية العرض فوق التطبيقات"
            "battery" -> "يرجى إيقاف تحسين البطارية للتطبيق"
            else -> "يحتاج التطبيق لصلاحيات إضافية"
        }
        
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_ALERTS)
            .setContentTitle(title)
            .setContentText(text)
            .setSmallIcon(R.drawable.ic_settings)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .build()
        
        notificationManager.notify(permissionType.hashCode(), notification)
    }
    
    fun cancelNotification(context: Context, notificationId: Int) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancel(notificationId)
    }
    
    fun cancelAllNotifications(context: Context) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancelAll()
    }
    
    fun updateServiceNotification(context: Context, status: String, details: String? = null) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_SERVICE)
            .setContentTitle("TV Controller - $status")
            .setContentText(details ?: context.getString(R.string.service_description))
            .setSmallIcon(R.drawable.ic_tv_control)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
        
        notificationManager.notify(NOTIFICATION_ID_SERVICE, notification)
    }
}
