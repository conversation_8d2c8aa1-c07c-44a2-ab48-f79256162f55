# TV Controller App - تطبيق التحكم في التلفزيون 📺

تطبيق Android TV متقدم للتحكم عن بعد مع خدمة المقدمة وشاشة التراكب.

## 🚀 البناء السريع

### ✅ الطريقة الموصى بها - Android Studio:
1. حمل Android Studio من: https://developer.android.com/studio
2. افتح هذا المجلد كمشروع في Android Studio
3. انتظر Gradle sync (5-10 دقائق)
4. Build > Build Bundle(s) / APK(s) > Build APK(s)
5. ستجد APK في: `app/build/outputs/apk/debug/app-debug.apk`

**📖 للتعليمات المفصلة خطوة بخطوة: [ANDROID_STUDIO_GUIDE.md](ANDROID_STUDIO_GUIDE.md)**

### 🔧 طريقة سطر الأوامر (إذا كان Gradle يعمل):
```cmd
gradlew assembleDebug
```

**📋 للمزيد من الخيارات: [BUILD_INSTRUCTIONS.md](BUILD_INSTRUCTIONS.md)**

## المميزات الرئيسية ✨

### 🔄 خدمة المقدمة (Foreground Service)
- يعمل في الخلفية باستمرار
- لا يتم إغلاقه من قبل النظام
- إشعار دائم يظهر حالة الاتصال

### 🖥️ شاشة التراكب (Overlay Screen)  
- تظهر فوق جميع التطبيقات
- تحكم سريع دون الحاجة لفتح التطبيق
- شفافية قابلة للتخصيص

### 🌐 اتصال الخادم
- دعم WebSocket للاتصال السريع
- دعم TCP كبديل
- إعادة الاتصال التلقائي

### 🔄 إعادة التشغيل التلقائي
- يعيد تشغيل نفسه عند الإغلاق
- يبدأ تلقائياً عند تشغيل الجهاز
- مقاوم للإغلاق القسري

### 📱 توافق واسع
- Android 5.0+ (API 21+)
- يعمل على أجهزة Android TV القديمة
- لا يحتاج تفعيل خيارات المطور

### 🚫 تجاوز قيود المطور
- يعمل بدون تفعيل "خيارات المطور"
- لا يحتاج "USB Debugging"
- تثبيت مباشر من APK

## 📦 التثبيت

### الطريقة الأولى: ADB (الأسرع)
```cmd
adb connect [TV_IP]:5555
adb install app-debug.apk
```

### الطريقة الثانية: USB
1. انسخ APK إلى USB
2. أدخل USB في التلفزيون
3. استخدم File Manager للتثبيت

### الطريقة الثالثة: تطبيق File Manager
1. ارفع APK إلى Google Drive
2. حمل File Manager على التلفزيون
3. حمل وثبت APK

## 🎮 الاستخدام

1. **التشغيل:** يبدأ تلقائياً بعد التثبيت
2. **الإعدادات:** اضغط أيقونة الإعدادات
3. **الاتصال:** أدخل عنوان IP للخادم
4. **التحكم:** استخدم الأزرار للتحكم

## 🔧 استكشاف الأخطاء

### مشكلة البناء:
```cmd
fix_gradle.bat
```

### مشكلة Java:
- حمل Java JDK من Oracle
- أضف Java إلى PATH

### مشكلة Android SDK:
- ثبت Android Studio
- أو حمل Command Line Tools

## 📁 هيكل المشروع

```
TV Controller App/
├── app/
│   ├── src/main/
│   │   ├── java/com/tvcontroller/app/
│   │   │   ├── MainActivity.kt
│   │   │   ├── SettingsActivity.kt
│   │   │   ├── service/OverlayService.kt
│   │   │   └── receiver/BootReceiver.kt
│   │   ├── res/
│   │   └── AndroidManifest.xml
│   └── build.gradle
├── BUILD_INSTRUCTIONS.md
└── README.md
```

## 🌟 الميزات التقنية

- **Kotlin:** لغة البرمجة الحديثة
- **ViewBinding:** ربط آمن للواجهات
- **WebSocket:** اتصال سريع ومستقر
- **Foreground Service:** خدمة مقدمة مستمرة
- **System Overlay:** عرض فوق التطبيقات
- **Auto-restart:** إعادة تشغيل ذكية

## 📞 الدعم

للمساعدة والدعم:
1. تحقق من [BUILD_INSTRUCTIONS.md](BUILD_INSTRUCTIONS.md)
2. تأكد من متطلبات النظام
3. جرب الحلول المقترحة

---

**تم تطوير هذا التطبيق خصيصاً لأجهزة Android TV مع التركيز على الاستقرار والأداء.**
