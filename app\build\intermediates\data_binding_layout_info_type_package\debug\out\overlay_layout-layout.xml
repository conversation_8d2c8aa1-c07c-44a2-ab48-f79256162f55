<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="overlay_layout" modulePackage="com.tvcontroller.app" filePath="app\src\main\res\layout\overlay_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/overlay_layout_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="165" endOffset="16"/></Target><Target id="@+id/ll_top_bar" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="23" endOffset="18"/></Target><Target id="@+id/progress_indicator" view="View"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="45"/></Target><Target id="@+id/tv_scrolling_text" view="TextView"><Expressions/><location startLine="26" startOffset="4" endLine="40" endOffset="45"/></Target><Target id="@+id/iv_lock_icon" view="ImageView"><Expressions/><location startLine="51" startOffset="8" endLine="57" endOffset="48"/></Target><Target id="@+id/tv_overlay_message" view="TextView"><Expressions/><location startLine="60" startOffset="8" endLine="69" endOffset="48"/></Target><Target id="@+id/tv_countdown" view="TextView"><Expressions/><location startLine="72" startOffset="8" endLine="81" endOffset="39"/></Target><Target id="@+id/tv_warning" view="TextView"><Expressions/><location startLine="84" startOffset="8" endLine="94" endOffset="39"/></Target><Target id="@+id/tv_connection_status" view="TextView"><Expressions/><location startLine="109" startOffset="8" endLine="117" endOffset="37"/></Target><Target id="@+id/tv_current_time" view="TextView"><Expressions/><location startLine="120" startOffset="8" endLine="127" endOffset="38"/></Target><Target id="@+id/tv_server_info" view="TextView"><Expressions/><location startLine="130" startOffset="8" endLine="138" endOffset="35"/></Target><Target id="@+id/animated_circle_1" view="View"><Expressions/><location startLine="143" startOffset="4" endLine="152" endOffset="29"/></Target><Target id="@+id/animated_circle_2" view="View"><Expressions/><location startLine="154" startOffset="4" endLine="163" endOffset="29"/></Target></Targets></Layout>