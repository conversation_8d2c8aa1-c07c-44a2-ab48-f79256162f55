@echo off
echo Building TV Controller APK...
echo.

echo Creating keystore...
if not exist "app\release-key.keystore" (
    keytool -genkey -v -keystore app\release-key.keystore -alias tvcontroller -keyalg RSA -keysize 2048 -validity 10000 -storepass tvcontroller123 -keypass tvcontroller123 -dname "CN=TV Controller, OU=Development, O=TV Controller App, L=City, S=State, C=US"
)

echo.
echo Building APK...
call gradlew assembleRelease

echo.
echo Done! APK location:
echo app\build\outputs\apk\release\app-release.apk
echo.
pause
