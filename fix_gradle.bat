@echo off
echo Fixing Gradle Wrapper...
echo.

echo Creating gradle wrapper directory...
if not exist "gradle\wrapper" mkdir gradle\wrapper

echo Downloading gradle-wrapper.jar...
powershell -Command "Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.4.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'gradle\wrapper\gradle-wrapper.jar'"

if exist "gradle\wrapper\gradle-wrapper.jar" (
    echo ✅ gradle-wrapper.jar downloaded successfully!
) else (
    echo ❌ Failed to download gradle-wrapper.jar
    echo.
    echo Manual fix:
    echo 1. Download gradle-wrapper.jar from: https://github.com/gradle/gradle/raw/v8.4.0/gradle/wrapper/gradle-wrapper.jar
    echo 2. Place it in: gradle\wrapper\gradle-wrapper.jar
    pause
    exit /b 1
)

echo.
echo Testing gradlew...
call gradlew --version

if %errorlevel%==0 (
    echo ✅ Gradle wrapper fixed successfully!
) else (
    echo ❌ Gradle wrapper still has issues
)

echo.
pause
