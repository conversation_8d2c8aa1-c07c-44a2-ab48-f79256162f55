package com.tvcontroller.app

import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.preference.PreferenceManager
import com.tvcontroller.app.databinding.ActivityMainBinding
import com.tvcontroller.app.service.OverlayService

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var sharedPreferences: SharedPreferences
    private var isServiceRunning = false

    companion object {
        private const val REQUEST_OVERLAY_PERMISSION = 1001
        private const val REQUEST_BATTERY_OPTIMIZATION = 1002
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        sharedPreferences = PreferenceManager.getDefaultSharedPreferences(this)

        setupUI()
        checkPermissions()
        updateServiceStatus()
        updateDeviceInfo()

        // Start services if needed
        startServicesIfNeeded()
    }

    private fun setupUI() {
        // Set up button listeners
        binding.btnStartService.setOnClickListener {
            if (checkAllPermissions()) {
                startOverlayService()
            }
        }

        binding.btnStopService.setOnClickListener {
            stopOverlayService()
        }

        binding.btnSettings.setOnClickListener {
            startActivity(Intent(this, SettingsActivity::class.java))
        }

        binding.btnTestConnection.setOnClickListener {
            testServerConnection()
        }

        binding.btnShowOverlay.setOnClickListener {
            if (checkAllPermissions()) {
                showTestOverlay()
            }
        }

        // Update connection status
        updateConnectionStatus()
    }

    private fun updateDeviceInfo() {
        binding.tvDeviceInfo.text = "Device: Android TV\nStatus: Ready"
    }

    private fun checkPermissions() {
        if (!checkOverlayPermission()) {
            showOverlayPermissionDialog()
        }
    }

    private fun checkAllPermissions(): Boolean {
        if (!checkOverlayPermission()) {
            showOverlayPermissionDialog()
            return false
        }

        if (!PermissionHelper.isBatteryOptimizationDisabled(this)) {
            showBatteryOptimizationDialog()
            return false
        }

        return true
    }

    private fun checkOverlayPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(this)
        } else {
            true
        }
    }

    private fun startServicesIfNeeded() {
        if (checkOverlayPermission()) {
            startOverlayService()
        }
    }

    private fun showOverlayPermissionDialog() {
        AlertDialog.Builder(this)
            .setTitle("Overlay Permission")
            .setMessage("This app needs overlay permission to work properly.")
            .setPositiveButton("Go to Settings") { _, _ ->
                requestOverlayPermission()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun requestOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)
            intent.data = Uri.parse("package:$packageName")
            startActivityForResult(intent, REQUEST_OVERLAY_PERMISSION)
        }
    }



    private fun startOverlayService() {
        val intent = Intent(this, OverlayService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }
        isServiceRunning = true
        updateServiceStatus()
        Toast.makeText(this, "تم تشغيل الخدمة", Toast.LENGTH_SHORT).show()
    }

    private fun stopOverlayService() {
        val intent = Intent(this, OverlayService::class.java)
        stopService(intent)
        isServiceRunning = false
        updateServiceStatus()
        Toast.makeText(this, "تم إيقاف الخدمة", Toast.LENGTH_SHORT).show()
    }

    private fun updateServiceStatus() {
        if (isServiceRunning) {
            binding.tvServiceStatus.text = "الخدمة تعمل"
            binding.btnStartService.isEnabled = false
            binding.btnStopService.isEnabled = true
        } else {
            binding.tvServiceStatus.text = "الخدمة متوقفة"
            binding.btnStartService.isEnabled = true
            binding.btnStopService.isEnabled = false
        }
    }

    private fun updateConnectionStatus() {
        val serverIp = sharedPreferences.getString("server_ip", "*************") ?: "*************"
        val serverPort = sharedPreferences.getInt("server_port", 8080)

        binding.tvServerInfo.text = "الخادم: $serverIp:$serverPort"
        binding.tvConnectionStatus.text = "غير متصل"
    }

    private fun testServerConnection() {
        Toast.makeText(this, "اختبار الاتصال...", Toast.LENGTH_SHORT).show()

        Thread {
            try {
                val serverIp = sharedPreferences.getString("server_ip", "*************") ?: "*************"
                val serverPort = sharedPreferences.getInt("server_port", 8080)

                val socket = java.net.Socket()
                socket.connect(java.net.InetSocketAddress(serverIp, serverPort), 5000)
                socket.close()

                runOnUiThread {
                    binding.tvConnectionStatus.text = "متصل"
                    Toast.makeText(this, "الاتصال ناجح!", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                runOnUiThread {
                    binding.tvConnectionStatus.text = "غير متصل"
                    Toast.makeText(this, "فشل الاتصال: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }.start()
    }

    private fun showTestOverlay() {
        val intent = Intent(this, OverlayService::class.java)
        intent.putExtra("action", "show_test_overlay")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            REQUEST_OVERLAY_PERMISSION -> {
                if (checkOverlayPermission()) {
                    Toast.makeText(this, "تم منح صلاحية العرض", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "لم يتم منح الصلاحية", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        updateServiceStatus()
        updateConnectionStatus()
        updateDeviceInfo()
    }
}
