package com.tvcontroller.app

import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.preference.PreferenceManager
import com.tvcontroller.app.databinding.ActivityMainBinding
import com.tvcontroller.app.service.OverlayService
import com.tvcontroller.app.utils.PermissionHelper
import com.tvcontroller.app.utils.ServiceHelper
import com.tvcontroller.app.worker.BackgroundWorker

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var sharedPreferences: SharedPreferences
    private var isServiceRunning = false

    companion object {
        private const val REQUEST_OVERLAY_PERMISSION = 1001
        private const val REQUEST_BATTERY_OPTIMIZATION = 1002
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        sharedPreferences = PreferenceManager.getDefaultSharedPreferences(this)

        setupUI()
        checkPermissions()
        updateServiceStatus()
        updateDeviceInfo()

        // Schedule background monitoring
        BackgroundWorker.schedulePeriodicWork(this)
    }

    private fun setupUI() {
        // Set up button listeners
        binding.btnStartService.setOnClickListener {
            if (checkAllPermissions()) {
                startOverlayService()
            }
        }

        binding.btnStopService.setOnClickListener {
            stopOverlayService()
        }

        binding.btnSettings.setOnClickListener {
            startActivity(Intent(this, SettingsActivity::class.java))
        }

        binding.btnTestConnection.setOnClickListener {
            testServerConnection()
        }

        binding.btnShowOverlay.setOnClickListener {
            if (checkAllPermissions()) {
                showTestOverlay()
            }
        }

        // Update connection status
        updateConnectionStatus()
    }

    private fun updateDeviceInfo() {
        val deviceInfo = PermissionHelper.getDeviceInfo()
        val systemInfo = ServiceHelper.getSystemInfo(this)
        val permissionStatus = PermissionHelper.checkAllPermissions(this)

        val fullInfo = buildString {
            append(deviceInfo)
            append("\n")
            append(systemInfo)
            append("\n")
            append(permissionStatus.getStatusText())
        }

        binding.tvDeviceInfo.text = fullInfo
    }

    private fun checkPermissions() {
        if (!PermissionHelper.hasOverlayPermission(this)) {
            showOverlayPermissionDialog()
        } else if (!PermissionHelper.isBatteryOptimizationDisabled(this)) {
            showBatteryOptimizationDialog()
        }
    }

    private fun checkAllPermissions(): Boolean {
        if (!PermissionHelper.hasOverlayPermission(this)) {
            showOverlayPermissionDialog()
            return false
        }

        if (!PermissionHelper.isBatteryOptimizationDisabled(this)) {
            showBatteryOptimizationDialog()
            return false
        }

        return true
    }

    private fun showOverlayPermissionDialog() {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.permission_overlay_title))
            .setMessage(getString(R.string.permission_overlay_message))
            .setPositiveButton(getString(R.string.go_to_settings)) { _, _ ->
                requestOverlayPermission()
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }

    private fun showBatteryOptimizationDialog() {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.permission_battery_title))
            .setMessage(getString(R.string.permission_battery_message))
            .setPositiveButton(getString(R.string.go_to_settings)) { _, _ ->
                requestBatteryOptimizationDisable()
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }

    private fun requestOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)
            intent.data = Uri.parse("package:$packageName")
            startActivityForResult(intent, REQUEST_OVERLAY_PERMISSION)
        }
    }

    private fun requestBatteryOptimizationDisable() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
            intent.data = Uri.parse("package:$packageName")
            startActivityForResult(intent, REQUEST_BATTERY_OPTIMIZATION)
        }
    }

    private fun startOverlayService() {
        val intent = Intent(this, OverlayService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }
        isServiceRunning = true
        updateServiceStatus()
        Toast.makeText(this, "تم تشغيل الخدمة", Toast.LENGTH_SHORT).show()
    }

    private fun stopOverlayService() {
        val intent = Intent(this, OverlayService::class.java)
        stopService(intent)
        isServiceRunning = false
        updateServiceStatus()
        Toast.makeText(this, "تم إيقاف الخدمة", Toast.LENGTH_SHORT).show()
    }

    private fun updateServiceStatus() {
        isServiceRunning = ServiceHelper.isServiceRunning(this, OverlayService::class.java)

        if (isServiceRunning) {
            binding.tvServiceStatus.text = "الخدمة تعمل"
            binding.tvServiceStatus.setTextColor(getColor(R.color.status_connected))
            binding.btnStartService.isEnabled = false
            binding.btnStopService.isEnabled = true
        } else {
            binding.tvServiceStatus.text = "الخدمة متوقفة"
            binding.tvServiceStatus.setTextColor(getColor(R.color.status_disconnected))
            binding.btnStartService.isEnabled = true
            binding.btnStopService.isEnabled = false
        }
    }

    private fun updateConnectionStatus() {
        val serverIp = sharedPreferences.getString("server_ip", "*************") ?: "*************"
        val serverPort = sharedPreferences.getInt("server_port", 8080)

        binding.tvServerInfo.text = "الخادم: $serverIp:$serverPort"
        binding.tvConnectionStatus.text = getString(R.string.status_disconnected)
        binding.tvConnectionStatus.setTextColor(getColor(R.color.status_disconnected))
    }

    private fun testServerConnection() {
        Toast.makeText(this, "اختبار الاتصال...", Toast.LENGTH_SHORT).show()

        Thread {
            try {
                val serverIp = sharedPreferences.getString("server_ip", "*************") ?: "*************"
                val serverPort = sharedPreferences.getInt("server_port", 8080)

                val socket = java.net.Socket()
                socket.connect(java.net.InetSocketAddress(serverIp, serverPort), 5000)
                socket.close()

                runOnUiThread {
                    binding.tvConnectionStatus.text = getString(R.string.status_connected)
                    binding.tvConnectionStatus.setTextColor(getColor(R.color.status_connected))
                    Toast.makeText(this, "الاتصال ناجح!", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                runOnUiThread {
                    binding.tvConnectionStatus.text = getString(R.string.status_disconnected)
                    binding.tvConnectionStatus.setTextColor(getColor(R.color.status_disconnected))
                    Toast.makeText(this, "فشل الاتصال: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }.start()
    }

    private fun showTestOverlay() {
        val intent = Intent(this, OverlayService::class.java)
        intent.putExtra("action", "show_test_overlay")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            REQUEST_OVERLAY_PERMISSION -> {
                if (PermissionHelper.hasOverlayPermission(this)) {
                    Toast.makeText(this, "تم منح صلاحية العرض", Toast.LENGTH_SHORT).show()
                    checkPermissions()
                } else {
                    Toast.makeText(this, "لم يتم منح الصلاحية", Toast.LENGTH_SHORT).show()
                }
            }
            REQUEST_BATTERY_OPTIMIZATION -> {
                if (PermissionHelper.isBatteryOptimizationDisabled(this)) {
                    Toast.makeText(this, "تم إيقاف تحسين البطارية", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "لم يتم إيقاف تحسين البطارية", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        updateServiceStatus()
        updateConnectionStatus()
        updateDeviceInfo()
    }
}
