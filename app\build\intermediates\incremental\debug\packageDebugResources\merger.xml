<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\shasha\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\shasha\app\src\main\res"><file name="animated_circle" path="C:\shasha\app\src\main\res\drawable\animated_circle.xml" qualifiers="" type="drawable"/><file name="ic_lock_screen" path="C:\shasha\app\src\main\res\drawable\ic_lock_screen.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\shasha\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_tv_control" path="C:\shasha\app\src\main\res\drawable\ic_tv_control.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\shasha\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_settings" path="C:\shasha\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="overlay_layout" path="C:\shasha\app\src\main\res\layout\overlay_layout.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\shasha\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file path="C:\shasha\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="red">#FFFF0000</color><color name="green">#FF00FF00</color><color name="blue">#FF0000FF</color><color name="yellow">#FFFFFF00</color><color name="orange">#FFFF8000</color><color name="purple">#FF800080</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="primary_color">#FF2196F3</color><color name="primary_dark_color">#FF1976D2</color><color name="accent_color">#FFFF5722</color><color name="status_connected">#FF4CAF50</color><color name="status_disconnected">#FFF44336</color><color name="status_connecting">#FFFF9800</color><color name="overlay_background">#FF000000</color><color name="overlay_text">#FFFFFFFF</color><color name="overlay_progress">#FFFF5722</color><color name="transparent">#00000000</color><color name="semi_transparent">#80000000</color></file><file path="C:\shasha\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">TV Controller</string><string name="app_description">Remote TV Screen Controller</string><string name="welcome_title">مرحباً بك في تطبيق التحكم بالتلفزيون</string><string name="welcome_subtitle">Welcome to TV Controller App</string><string name="status_connected">متصل</string><string name="status_disconnected">غير متصل</string><string name="status_connecting">جاري الاتصال...</string><string name="settings">الإعدادات</string><string name="server_settings">إعدادات الخادم</string><string name="server_ip">عنوان IP للخادم</string><string name="server_port">منفذ الخادم</string><string name="language_settings">إعدادات اللغة</string><string name="arabic">العربية</string><string name="english">English</string><string name="save">حفظ</string><string name="cancel">إلغاء</string><string name="overlay_message_default">الشاشة مقفلة - Screen Locked</string><string name="overlay_countdown">الوقت المتبقي: %d ثانية</string><string name="overlay_expired">انتهى الوقت - Time Expired</string><string name="service_running">خدمة التحكم بالتلفزيون تعمل</string><string name="service_description">يتم تشغيل الخدمة في الخلفية للتحكم في الشاشة</string><string name="permission_overlay_title">صلاحية العرض فوق التطبيقات</string><string name="permission_overlay_message">يحتاج التطبيق إلى صلاحية العرض فوق التطبيقات الأخرى للعمل بشكل صحيح</string><string name="permission_battery_title">تحسين البطارية</string><string name="permission_battery_message">يرجى إيقاف تحسين البطارية لهذا التطبيق لضمان عمله المستمر</string><string name="go_to_settings">الذهاب للإعدادات</string><string name="error_connection">خطأ في الاتصال بالخادم</string><string name="error_permission">لم يتم منح الصلاحيات المطلوبة</string><string name="error_invalid_ip">عنوان IP غير صحيح</string><string name="error_invalid_port">رقم المنفذ غير صحيح</string><string name="start_service">تشغيل الخدمة</string><string name="stop_service">إيقاف الخدمة</string><string name="test_connection">اختبار الاتصال</string><string name="show_overlay">عرض الشاشة السوداء</string><string name="hide_overlay">إخفاء الشاشة السوداء</string></file><file path="C:\shasha\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.TVControllerApp" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/accent_color</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:statusBarColor">@color/primary_dark_color</item>
        
        <item name="android:windowBackground">@color/white</item>
    </style><style name="Theme.Overlay" parent="Theme.Material3.DayNight">
        <item name="android:windowBackground">@color/overlay_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowIsFloating">false</item>
    </style><style name="Theme.TVControllerApp.TV" parent="@style/Theme.Leanback">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="android:windowBackground">@color/primary_dark_color</item>
    </style></file><file name="backup_rules" path="C:\shasha\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\shasha\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\shasha\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\shasha\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\shasha\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\shasha\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>