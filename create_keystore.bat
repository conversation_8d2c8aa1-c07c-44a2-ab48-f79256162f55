@echo off
echo Creating keystore for TV Controller App...

keytool -genkey -v -keystore app\release-key.keystore -alias tvcontroller -keyalg RSA -keysize 2048 -validity 10000 -storepass tvcontroller123 -keypass tvcontroller123 -dname "CN=TV Controller, OU=Development, O=TV Controller App, L=City, S=State, C=US"

if %ERRORLEVEL% EQU 0 (
    echo Keystore created successfully!
) else (
    echo Failed to create keystore. Make sure Java keytool is in your PATH.
)

pause
