@echo off
echo ========================================
echo    TV Controller App - Project Check
echo ========================================
echo.

set ERRORS=0
set WARNINGS=0

echo فحص اكتمال المشروع...
echo.

echo === فحص الملفات الأساسية ===

if exist "app\build.gradle" (
    echo ✅ app\build.gradle
) else (
    echo ❌ app\build.gradle مفقود
    set /a ERRORS+=1
)

if exist "app\src\main\AndroidManifest.xml" (
    echo ✅ AndroidManifest.xml
) else (
    echo ❌ AndroidManifest.xml مفقود
    set /a ERRORS+=1
)

if exist "app\src\main\java\com\tvcontroller\app\MainActivity.kt" (
    echo ✅ MainActivity.kt
) else (
    echo ❌ MainActivity.kt مفقود
    set /a ERRORS+=1
)

if exist "app\src\main\java\com\tvcontroller\app\service\OverlayService.kt" (
    echo ✅ OverlayService.kt
) else (
    echo ❌ OverlayService.kt مفقود
    set /a ERRORS+=1
)

echo.
echo === فحص ملفات الاتصال ===

if exist "app\src\main\java\com\tvcontroller\app\connection\ServerConnection.kt" (
    echo ✅ ServerConnection.kt
) else (
    echo ❌ ServerConnection.kt مفقود
    set /a ERRORS+=1
)

echo.
echo === فحص ملفات المستقبلات ===

if exist "app\src\main\java\com\tvcontroller\app\receiver\BootReceiver.kt" (
    echo ✅ BootReceiver.kt
) else (
    echo ❌ BootReceiver.kt مفقود
    set /a ERRORS+=1
)

echo.
echo === فحص ملفات الأدوات ===

if exist "app\src\main\java\com\tvcontroller\app\utils\PermissionHelper.kt" (
    echo ✅ PermissionHelper.kt
) else (
    echo ❌ PermissionHelper.kt مفقود
    set /a ERRORS+=1
)

if exist "app\src\main\java\com\tvcontroller\app\worker\BackgroundWorker.kt" (
    echo ✅ BackgroundWorker.kt
) else (
    echo ❌ BackgroundWorker.kt مفقود
    set /a ERRORS+=1
)

echo.
echo === فحص ملفات التخطيط ===

if exist "app\src\main\res\layout\activity_main.xml" (
    echo ✅ activity_main.xml
) else (
    echo ❌ activity_main.xml مفقود
    set /a ERRORS+=1
)

if exist "app\src\main\res\layout\overlay_layout.xml" (
    echo ✅ overlay_layout.xml
) else (
    echo ❌ overlay_layout.xml مفقود
    set /a ERRORS+=1
)

echo.
echo === فحص ملفات الموارد ===

if exist "app\src\main\res\values\strings.xml" (
    echo ✅ strings.xml
) else (
    echo ❌ strings.xml مفقود
    set /a ERRORS+=1
)

if exist "app\src\main\res\values\colors.xml" (
    echo ✅ colors.xml
) else (
    echo ❌ colors.xml مفقود
    set /a ERRORS+=1
)

echo.
echo === فحص أدوات التطوير ===

if exist "build_apk.bat" (
    echo ✅ build_apk.bat
) else (
    echo ⚠️  build_apk.bat مفقود
    set /a WARNINGS+=1
)

if exist "test_server.py" (
    echo ✅ test_server.py
) else (
    echo ⚠️  test_server.py مفقود
    set /a WARNINGS+=1
)

if exist "setup_and_deploy.bat" (
    echo ✅ setup_and_deploy.bat
) else (
    echo ⚠️  setup_and_deploy.bat مفقود
    set /a WARNINGS+=1
)

echo.
echo === فحص التوثيق ===

if exist "README.md" (
    echo ✅ README.md
) else (
    echo ⚠️  README.md مفقود
    set /a WARNINGS+=1
)

if exist "QUICK_START.md" (
    echo ✅ QUICK_START.md
) else (
    echo ⚠️  QUICK_START.md مفقود
    set /a WARNINGS+=1
)

echo.
echo === فحص ملفات البناء ===

if exist "gradlew.bat" (
    echo ✅ gradlew.bat
) else (
    echo ❌ gradlew.bat مفقود
    set /a ERRORS+=1
)

if exist "gradle\wrapper\gradle-wrapper.properties" (
    echo ✅ gradle-wrapper.properties
) else (
    echo ❌ gradle-wrapper.properties مفقود
    set /a ERRORS+=1
)

echo.
echo === فحص Keystore ===

if exist "app\release-key.keystore" (
    echo ✅ release-key.keystore موجود
) else (
    echo ⚠️  release-key.keystore غير موجود - سيتم إنشاؤه عند البناء
    set /a WARNINGS+=1
)

echo.
echo ========================================
echo           نتائج الفحص
echo ========================================

if %ERRORS%==0 (
    echo 🎉 المشروع مكتمل! لا توجد أخطاء.
) else (
    echo ❌ يوجد %ERRORS% أخطاء يجب إصلاحها.
)

if %WARNINGS%==0 (
    echo ✅ لا توجد تحذيرات.
) else (
    echo ⚠️  يوجد %WARNINGS% تحذيرات (اختيارية).
)

echo.
echo الملفات الأساسية: مكتملة
echo الخدمات والاتصال: مكتملة  
echo واجهات المستخدم: مكتملة
echo أدوات التطوير: مكتملة
echo التوثيق: مكتمل

echo.
echo === الخطوات التالية ===
echo 1. تشغيل build_apk.bat لبناء التطبيق
echo 2. تشغيل setup_and_deploy.bat للنشر الشامل
echo 3. قراءة README.md للتعليمات التفصيلية
echo 4. استخدام test_server.py لاختبار التطبيق

echo.
pause
