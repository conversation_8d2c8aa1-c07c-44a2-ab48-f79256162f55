// Generated by view binder compiler. Do not edit!
package com.tvcontroller.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tvcontroller.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class OverlayLayoutBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final View animatedCircle1;

  @NonNull
  public final View animatedCircle2;

  @NonNull
  public final ImageView ivLockIcon;

  @NonNull
  public final LinearLayout llTopBar;

  @NonNull
  public final View progressIndicator;

  @NonNull
  public final TextView tvConnectionStatus;

  @NonNull
  public final TextView tvCountdown;

  @NonNull
  public final TextView tvCurrentTime;

  @NonNull
  public final TextView tvOverlayMessage;

  @NonNull
  public final TextView tvScrollingText;

  @NonNull
  public final TextView tvServerInfo;

  @NonNull
  public final TextView tvWarning;

  private OverlayLayoutBinding(@NonNull RelativeLayout rootView, @NonNull View animatedCircle1,
      @NonNull View animatedCircle2, @NonNull ImageView ivLockIcon, @NonNull LinearLayout llTopBar,
      @NonNull View progressIndicator, @NonNull TextView tvConnectionStatus,
      @NonNull TextView tvCountdown, @NonNull TextView tvCurrentTime,
      @NonNull TextView tvOverlayMessage, @NonNull TextView tvScrollingText,
      @NonNull TextView tvServerInfo, @NonNull TextView tvWarning) {
    this.rootView = rootView;
    this.animatedCircle1 = animatedCircle1;
    this.animatedCircle2 = animatedCircle2;
    this.ivLockIcon = ivLockIcon;
    this.llTopBar = llTopBar;
    this.progressIndicator = progressIndicator;
    this.tvConnectionStatus = tvConnectionStatus;
    this.tvCountdown = tvCountdown;
    this.tvCurrentTime = tvCurrentTime;
    this.tvOverlayMessage = tvOverlayMessage;
    this.tvScrollingText = tvScrollingText;
    this.tvServerInfo = tvServerInfo;
    this.tvWarning = tvWarning;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static OverlayLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static OverlayLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.overlay_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static OverlayLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.animated_circle_1;
      View animatedCircle1 = ViewBindings.findChildViewById(rootView, id);
      if (animatedCircle1 == null) {
        break missingId;
      }

      id = R.id.animated_circle_2;
      View animatedCircle2 = ViewBindings.findChildViewById(rootView, id);
      if (animatedCircle2 == null) {
        break missingId;
      }

      id = R.id.iv_lock_icon;
      ImageView ivLockIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivLockIcon == null) {
        break missingId;
      }

      id = R.id.ll_top_bar;
      LinearLayout llTopBar = ViewBindings.findChildViewById(rootView, id);
      if (llTopBar == null) {
        break missingId;
      }

      id = R.id.progress_indicator;
      View progressIndicator = ViewBindings.findChildViewById(rootView, id);
      if (progressIndicator == null) {
        break missingId;
      }

      id = R.id.tv_connection_status;
      TextView tvConnectionStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvConnectionStatus == null) {
        break missingId;
      }

      id = R.id.tv_countdown;
      TextView tvCountdown = ViewBindings.findChildViewById(rootView, id);
      if (tvCountdown == null) {
        break missingId;
      }

      id = R.id.tv_current_time;
      TextView tvCurrentTime = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentTime == null) {
        break missingId;
      }

      id = R.id.tv_overlay_message;
      TextView tvOverlayMessage = ViewBindings.findChildViewById(rootView, id);
      if (tvOverlayMessage == null) {
        break missingId;
      }

      id = R.id.tv_scrolling_text;
      TextView tvScrollingText = ViewBindings.findChildViewById(rootView, id);
      if (tvScrollingText == null) {
        break missingId;
      }

      id = R.id.tv_server_info;
      TextView tvServerInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvServerInfo == null) {
        break missingId;
      }

      id = R.id.tv_warning;
      TextView tvWarning = ViewBindings.findChildViewById(rootView, id);
      if (tvWarning == null) {
        break missingId;
      }

      return new OverlayLayoutBinding((RelativeLayout) rootView, animatedCircle1, animatedCircle2,
          ivLockIcon, llTopBar, progressIndicator, tvConnectionStatus, tvCountdown, tvCurrentTime,
          tvOverlayMessage, tvScrollingText, tvServerInfo, tvWarning);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
