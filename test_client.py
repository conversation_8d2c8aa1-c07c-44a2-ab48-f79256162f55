#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عميل اختبار لتطبيق التحكم في شاشات التلفزيون
Test Client for TV Controller App
"""

import socket
import time
import threading
from datetime import datetime

class TVControllerTestClient:
    def __init__(self, server_ip='*************', server_port=8080):
        self.server_ip = server_ip
        self.server_port = server_port
        self.socket = None
        self.connected = False
        
    def log(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def connect(self):
        """الاتصال بالخادم"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.server_ip, self.server_port))
            self.connected = True
            self.log(f"متصل بالخادم {self.server_ip}:{self.server_port}")
            
            # إرسال رسالة اتصال
            self.send_message("CLIENT_CONNECTED")
            
            return True
        except Exception as e:
            self.log(f"فشل الاتصال: {e}")
            return False
    
    def send_message(self, message):
        """إرسال رسالة للخادم"""
        if self.connected and self.socket:
            try:
                self.socket.send(message.encode('utf-8'))
                self.log(f"تم إرسال: {message}")
                return True
            except Exception as e:
                self.log(f"فشل الإرسال: {e}")
                self.connected = False
                return False
        return False
    
    def listen_for_commands(self):
        """الاستماع لأوامر الخادم"""
        while self.connected:
            try:
                data = self.socket.recv(1024)
                if data:
                    command = data.decode('utf-8').strip()
                    self.log(f"استلام أمر: {command}")
                    self.process_command(command)
                else:
                    break
            except socket.timeout:
                continue
            except Exception as e:
                self.log(f"خطأ في الاستماع: {e}")
                break
        
        self.connected = False
        self.log("انقطع الاتصال")
    
    def process_command(self, command):
        """معالجة الأوامر المستلمة"""
        self.log(f"معالجة الأمر: {command}")
        
        if command == "SHOW_BLACK_SCREEN":
            self.log("🖤 عرض الشاشة السوداء")
        elif command == "HIDE_BLACK_SCREEN":
            self.log("⚪ إخفاء الشاشة السوداء")
        elif command == "RESTART_APP":
            self.log("🔄 إعادة تشغيل التطبيق")
        elif command.startswith("MESSAGE:"):
            message = command[8:]
            self.log(f"💬 رسالة: {message}")
        elif command.startswith("COUNTDOWN:"):
            try:
                seconds = int(command[10:])
                self.log(f"⏰ عد تنازلي: {seconds} ثانية")
                self.simulate_countdown(seconds)
            except ValueError:
                self.log("خطأ في تحليل العد التنازلي")
        elif command == "PONG":
            self.log("🏓 استلام PONG")
        else:
            self.log(f"❓ أمر غير معروف: {command}")
    
    def simulate_countdown(self, seconds):
        """محاكاة العد التنازلي"""
        def countdown():
            for i in range(seconds, 0, -1):
                self.log(f"⏰ العد التنازلي: {i}")
                time.sleep(1)
            self.log("⏰ انتهى العد التنازلي!")
        
        countdown_thread = threading.Thread(target=countdown)
        countdown_thread.daemon = True
        countdown_thread.start()
    
    def ping_server(self):
        """إرسال ping للخادم"""
        return self.send_message("PING")
    
    def disconnect(self):
        """قطع الاتصال"""
        self.connected = False
        if self.socket:
            self.socket.close()
        self.log("تم قطع الاتصال")

def test_connection(ip, port):
    """اختبار الاتصال بالخادم"""
    print(f"اختبار الاتصال بـ {ip}:{port}...")
    
    try:
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.settimeout(5)
        result = test_socket.connect_ex((ip, port))
        test_socket.close()
        
        if result == 0:
            print("✅ الاتصال ناجح!")
            return True
        else:
            print("❌ فشل الاتصال!")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def main():
    print("📱 عميل اختبار تطبيق التحكم في شاشات التلفزيون")
    print("=" * 50)
    
    # إدخال معلومات الخادم
    server_ip = input("عنوان IP للخادم [*************]: ").strip()
    if not server_ip:
        server_ip = "*************"
    
    server_port = input("منفذ الخادم [8080]: ").strip()
    if not server_port:
        server_port = 8080
    else:
        try:
            server_port = int(server_port)
        except ValueError:
            print("منفذ غير صحيح، استخدام المنفذ الافتراضي 8080")
            server_port = 8080
    
    # اختبار الاتصال أولاً
    if not test_connection(server_ip, server_port):
        print("تأكد من تشغيل الخادم والإعدادات الصحيحة")
        return
    
    # إنشاء العميل
    client = TVControllerTestClient(server_ip, server_port)
    
    try:
        # الاتصال بالخادم
        if client.connect():
            # بدء الاستماع في thread منفصل
            listen_thread = threading.Thread(target=client.listen_for_commands)
            listen_thread.daemon = True
            listen_thread.start()
            
            print("\n=== وضع العميل التفاعلي ===")
            print("الأوامر المتاحة:")
            print("- ping: إرسال ping للخادم")
            print("- status: عرض حالة الاتصال")
            print("- quit: إنهاء البرنامج")
            print("=" * 30)
            
            # حلقة التفاعل
            while client.connected:
                try:
                    command = input("\nأدخل أمر: ").strip().lower()
                    
                    if command == "quit" or command == "exit":
                        break
                    elif command == "ping":
                        client.ping_server()
                    elif command == "status":
                        status = "متصل ✅" if client.connected else "غير متصل ❌"
                        print(f"حالة الاتصال: {status}")
                    elif command == "help":
                        print("راجع قائمة الأوامر أعلاه")
                    else:
                        print("أمر غير معروف. اكتب 'help' للمساعدة")
                        
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f"خطأ: {e}")
        
    except KeyboardInterrupt:
        pass
    finally:
        print("\nإغلاق العميل...")
        client.disconnect()
        print("تم إنهاء البرنامج")

if __name__ == "__main__":
    main()
