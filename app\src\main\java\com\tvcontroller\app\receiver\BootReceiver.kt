package com.tvcontroller.app.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.tvcontroller.app.service.OverlayService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootReceiver"
        private const val START_DELAY = 10000L // 10 seconds delay after boot
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Received broadcast: ${intent.action}")
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            "android.intent.action.QUICKBOOT_POWERON",
            "com.htc.intent.action.QUICKBOOT_POWERON",
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                
                Log.d(TAG, "Device booted or app updated, starting service")
                
                // Use coroutine to add delay and start service
                val scope = CoroutineScope(Dispatchers.Main)
                scope.launch {
                    // Wait for system to stabilize
                    delay(START_DELAY)
                    
                    try {
                        val serviceIntent = Intent(context, OverlayService::class.java)
                        
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            context.startForegroundService(serviceIntent)
                        } else {
                            context.startService(serviceIntent)
                        }
                        
                        Log.d(TAG, "OverlayService started successfully")
                        
                        // Also schedule periodic restart
                        schedulePeriodicRestart(context)
                        
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to start service: ${e.message}")
                        
                        // Retry after another delay
                        delay(30000) // 30 seconds
                        try {
                            val retryIntent = Intent(context, OverlayService::class.java)
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                context.startForegroundService(retryIntent)
                            } else {
                                context.startService(retryIntent)
                            }
                            Log.d(TAG, "Service started on retry")
                        } catch (retryException: Exception) {
                            Log.e(TAG, "Retry failed: ${retryException.message}")
                        }
                    }
                }
            }
        }
    }
    
    private fun schedulePeriodicRestart(context: Context) {
        // This will be implemented with WorkManager
        Log.d(TAG, "Scheduling periodic service restart")
        
        val scope = CoroutineScope(Dispatchers.IO)
        scope.launch {
            // Check service every 5 minutes and restart if needed
            while (true) {
                delay(300000) // 5 minutes
                
                try {
                    if (!isServiceRunning(context)) {
                        Log.d(TAG, "Service not running, restarting...")
                        
                        val serviceIntent = Intent(context, OverlayService::class.java)
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            context.startForegroundService(serviceIntent)
                        } else {
                            context.startService(serviceIntent)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in periodic check: ${e.message}")
                }
            }
        }
    }
    
    private fun isServiceRunning(context: Context): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val services = activityManager.getRunningServices(Integer.MAX_VALUE)
            
            for (service in services) {
                if (OverlayService::class.java.name == service.service.className) {
                    return true
                }
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking service status: ${e.message}")
            false
        }
    }
}
