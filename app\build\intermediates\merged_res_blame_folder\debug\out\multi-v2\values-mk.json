{"logs": [{"outputFile": "com.tvcontroller.app-mergeDebugResources-45:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4dde4ede0bc8a81ca43b3c1837b4aed4\\transformed\\material-1.11.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,352,432,514,611,700,796,920,1007,1073,1164,1234,1298,1401,1464,1529,1589,1657,1720,1775,1903,1960,2022,2077,2152,2292,2379,2462,2595,2677,2762,2908,2995,3072,3126,3181,3247,3320,3396,3485,3563,3636,3712,3787,3857,3966,4054,4129,4221,4313,4387,4461,4553,4606,4688,4755,4838,4925,4987,5051,5114,5184,5298,5413,5515,5627,5685,5744", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,145,86,76,53,54,65,72,75,88,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84", "endOffsets": "266,347,427,509,606,695,791,915,1002,1068,1159,1229,1293,1396,1459,1524,1584,1652,1715,1770,1898,1955,2017,2072,2147,2287,2374,2457,2590,2672,2757,2903,2990,3067,3121,3176,3242,3315,3391,3480,3558,3631,3707,3782,3852,3961,4049,4124,4216,4308,4382,4456,4548,4601,4683,4750,4833,4920,4982,5046,5109,5179,5293,5408,5510,5622,5680,5739,5824"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3036,3117,3197,3279,3376,4184,4280,4404,4562,4715,4806,4876,4940,5043,5106,5171,5231,5299,5362,5417,5545,5602,5664,5719,5794,5934,6021,6104,6237,6319,6404,6550,6637,6714,6768,6823,6889,6962,7038,7127,7205,7278,7354,7429,7499,7608,7696,7771,7863,7955,8029,8103,8195,8248,8330,8397,8480,8567,8629,8693,8756,8826,8940,9055,9157,9269,9327,9467", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,110", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,145,86,76,53,54,65,72,75,88,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84", "endOffsets": "316,3112,3192,3274,3371,3460,4275,4399,4486,4623,4801,4871,4935,5038,5101,5166,5226,5294,5357,5412,5540,5597,5659,5714,5789,5929,6016,6099,6232,6314,6399,6545,6632,6709,6763,6818,6884,6957,7033,7122,7200,7273,7349,7424,7494,7603,7691,7766,7858,7950,8024,8098,8190,8243,8325,8392,8475,8562,8624,8688,8751,8821,8935,9050,9152,9264,9322,9381,9547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\566c99b1f2c65122017c8029b64a207c\\transformed\\appcompat-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2900"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,533,641,727,835,954,1038,1119,1210,1303,1399,1493,1593,1686,1781,1877,1968,2059,2146,2252,2358,2459,2566,2678,2782,2938,9696", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "424,528,636,722,830,949,1033,1114,1205,1298,1394,1488,1588,1681,1776,1872,1963,2054,2141,2247,2353,2454,2561,2673,2777,2933,3031,9776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ba6274a1892fdd5def28bdd9767110ee\\transformed\\core-1.12.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,113", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3465,3563,3665,3762,3860,3965,4068,9781", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "3558,3660,3757,3855,3960,4063,4179,9877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8944fc038c0e2d5da876e70d3ee0f81b\\transformed\\preference-1.2.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,344,488,657,743", "endColumns": "70,86,80,143,168,85,81", "endOffsets": "171,258,339,483,652,738,820"}, "to": {"startLines": "48,50,109,111,114,115,116", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4491,4628,9386,9552,9882,10051,10137", "endColumns": "70,86,80,143,168,85,81", "endOffsets": "4557,4710,9462,9691,10046,10132,10214"}}]}]}