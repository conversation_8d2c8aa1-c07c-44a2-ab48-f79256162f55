@echo off
echo ========================================
echo      Disk Space Cleanup
echo ========================================
echo.

echo Current disk space:
dir c:\ | findstr "bytes free"
echo.

echo Cleaning temporary files...

REM Clean Windows temp files
echo Cleaning Windows temp files...
del /q /f /s "%TEMP%\*" 2>nul
del /q /f /s "C:\Windows\Temp\*" 2>nul

REM Clean browser cache (if exists)
echo Cleaning browser cache...
if exist "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" (
    del /q /f /s "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache\*" 2>nul
)

REM Clean Gradle cache (if exists)
echo Cleaning Gradle cache...
if exist "%USERPROFILE%\.gradle\caches" (
    rmdir /s /q "%USERPROFILE%\.gradle\caches" 2>nul
)

REM Clean Android build cache (if exists)
echo Cleaning Android build cache...
if exist "%USERPROFILE%\.android\build-cache" (
    rmdir /s /q "%USERPROFILE%\.android\build-cache" 2>nul
)

echo.
echo Running Disk Cleanup...
cleanmgr /sagerun:1

echo.
echo After cleanup disk space:
dir c:\ | findstr "bytes free"

echo.
echo If you have enough space now (>500MB), run:
echo fix_gradle.bat
echo.
echo Otherwise, consider:
echo 1. Moving project to another drive
echo 2. Using Android Studio directly
echo 3. Using online build service
echo.
pause
