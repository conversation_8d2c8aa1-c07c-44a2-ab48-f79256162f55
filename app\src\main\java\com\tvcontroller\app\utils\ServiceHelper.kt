package com.tvcontroller.app.utils

import android.app.ActivityManager
import android.content.Context
import android.util.Log

object ServiceHelper {
    
    private const val TAG = "ServiceHelper"
    
    /**
     * Check if a specific service is running
     */
    fun isServiceRunning(context: Context, serviceClass: Class<*>): <PERSON><PERSON>an {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val services = activityManager.getRunningServices(Integer.MAX_VALUE)
            
            for (service in services) {
                if (serviceClass.name == service.service.className) {
                    Log.d(TAG, "Service ${serviceClass.simpleName} is running")
                    return true
                }
            }
            
            Log.d(TAG, "Service ${serviceClass.simpleName} is not running")
            false
            
        } catch (e: Exception) {
            Log.e(TAG, "Error checking service status: ${e.message}")
            false
        }
    }
    
    /**
     * Get running services count
     */
    fun getRunningServicesCount(context: Context): Int {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val services = activityManager.getRunningServices(Integer.MAX_VALUE)
            services.size
        } catch (e: Exception) {
            Log.e(TAG, "Error getting running services count: ${e.message}")
            0
        }
    }
    
    /**
     * Get memory info
     */
    fun getMemoryInfo(context: Context): ActivityManager.MemoryInfo {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        return memoryInfo
    }
    
    /**
     * Check if device has low memory
     */
    fun isLowMemory(context: Context): Boolean {
        return getMemoryInfo(context).lowMemory
    }
    
    /**
     * Get available memory in MB
     */
    fun getAvailableMemoryMB(context: Context): Long {
        val memoryInfo = getMemoryInfo(context)
        return memoryInfo.availMem / (1024 * 1024)
    }
    
    /**
     * Get total memory in MB
     */
    fun getTotalMemoryMB(context: Context): Long {
        val memoryInfo = getMemoryInfo(context)
        return memoryInfo.totalMem / (1024 * 1024)
    }
    
    /**
     * Get system info for debugging
     */
    fun getSystemInfo(context: Context): String {
        return try {
            val memoryInfo = getMemoryInfo(context)
            val availableMB = memoryInfo.availMem / (1024 * 1024)
            val totalMB = memoryInfo.totalMem / (1024 * 1024)
            val usedMB = totalMB - availableMB
            val usagePercent = (usedMB * 100) / totalMB
            
            buildString {
                append("Memory Usage: $usedMB MB / $totalMB MB ($usagePercent%)\n")
                append("Available Memory: $availableMB MB\n")
                append("Low Memory: ${if (memoryInfo.lowMemory) "Yes" else "No"}\n")
                append("Running Services: ${getRunningServicesCount(context)}\n")
            }
        } catch (e: Exception) {
            "Error getting system info: ${e.message}"
        }
    }
}
