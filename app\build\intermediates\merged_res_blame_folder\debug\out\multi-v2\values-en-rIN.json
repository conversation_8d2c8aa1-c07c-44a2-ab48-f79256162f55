{"logs": [{"outputFile": "com.tvcontroller.app-mergeDebugResources-47:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b8416fff55a3441d3e464c90290ecef7\\transformed\\leanback-1.0.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,505,624,736,832,928,1059,1188,1293,1414,1542,1663,1782,1887,1978,2106,2195,2296,2399,2500,2593,2703,2809,2920,3029,3128,3235,3345,3461,3567,3679,3766,3850,3950,4085,4236", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "207,308,406,500,619,731,827,923,1054,1183,1288,1409,1537,1658,1777,1882,1973,2101,2190,2291,2394,2495,2588,2698,2804,2915,3024,3123,3230,3340,3456,3562,3674,3761,3845,3945,4080,4231,4321"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3636,3743,3844,3942,4036,4155,4267,4363,4459,4590,4719,4824,4945,5073,5194,5313,5418,5509,5637,5726,5827,5930,6031,6124,6234,6340,6451,6560,6659,6766,6876,6992,7098,7210,7297,7381,7481,7616,7841", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "3738,3839,3937,4031,4150,4262,4358,4454,4585,4714,4819,4940,5068,5189,5308,5413,5504,5632,5721,5822,5925,6026,6119,6229,6335,6446,6555,6654,6761,6871,6987,7093,7205,7292,7376,7476,7611,7762,7926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\566c99b1f2c65122017c8029b64a207c\\transformed\\appcompat-1.6.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,79", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,8065", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,8143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8944fc038c0e2d5da876e70d3ee0f81b\\transformed\\preference-1.2.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "36,37,76,78,81,82,83", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3481,3549,7767,7931,8249,8418,8498", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "3544,3631,7836,8060,8413,8493,8569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ba6274a1892fdd5def28bdd9767110ee\\transformed\\core-1.12.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "29,30,31,32,33,34,35,80", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2762,2858,2960,3059,3158,3262,3365,8148", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "2853,2955,3054,3153,3257,3360,3476,8244"}}]}]}