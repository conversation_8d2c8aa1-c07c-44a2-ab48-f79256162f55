<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal">

        <!-- App Title -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/welcome_title"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@color/primary_color"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/welcome_subtitle"
            android:textSize="18sp"
            android:textColor="@color/primary_dark_color"
            android:gravity="center"
            android:layout_marginBottom="32dp" />

        <!-- Status Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="حالة الخدمة - Service Status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tv_service_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="الخدمة متوقفة"
                    android:textSize="16sp"
                    android:textColor="@color/status_disconnected"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_connection_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/status_disconnected"
                    android:textSize="14sp"
                    android:textColor="@color/status_disconnected"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_server_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="الخادم: غير محدد"
                    android:textSize="14sp"
                    android:textColor="@color/black" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Control Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp">

            <Button
                android:id="@+id/btn_start_service"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/start_service"
                android:textSize="16sp"
                android:backgroundTint="@color/status_connected"
                android:layout_marginBottom="12dp" />

            <Button
                android:id="@+id/btn_stop_service"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/stop_service"
                android:textSize="16sp"
                android:backgroundTint="@color/status_disconnected"
                android:layout_marginBottom="12dp" />

            <Button
                android:id="@+id/btn_test_connection"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/test_connection"
                android:textSize="16sp"
                android:backgroundTint="@color/status_connecting"
                android:layout_marginBottom="12dp" />

            <Button
                android:id="@+id/btn_show_overlay"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/show_overlay"
                android:textSize="16sp"
                android:backgroundTint="@color/accent_color"
                android:layout_marginBottom="12dp" />

        </LinearLayout>

        <!-- Settings Button -->
        <Button
            android:id="@+id/btn_settings"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="@string/settings"
            android:textSize="16sp"
            android:backgroundTint="@color/primary_color"
            android:layout_marginBottom="24dp" />

        <!-- Device Info Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="معلومات الجهاز - Device Info"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tv_device_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="جاري التحميل..."
                    android:textSize="12sp"
                    android:textColor="@color/black"
                    android:fontFamily="monospace" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
