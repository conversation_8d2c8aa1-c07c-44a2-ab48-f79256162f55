# TV Controller APK Builder
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    TV Controller - Build APK" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check Java
Write-Host "Checking Java..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1
    Write-Host "✅ Java found: $($javaVersion[0])" -ForegroundColor Green
} catch {
    Write-Host "❌ Java not found!" -ForegroundColor Red
    exit 1
}

# Find keytool
Write-Host "Finding keytool..." -ForegroundColor Yellow
$keytoolPaths = @(
    "C:\Program Files\Java\jdk-24.0.1\bin\keytool.exe",
    "C:\Program Files\Java\jre-24.0.1\bin\keytool.exe",
    "C:\Program Files (x86)\Java\jdk-24.0.1\bin\keytool.exe",
    "C:\Program Files (x86)\Java\jre-24.0.1\bin\keytool.exe"
)

$keytool = $null
foreach ($path in $keytoolPaths) {
    if (Test-Path $path) {
        $keytool = $path
        break
    }
}

if (-not $keytool) {
    # Search for keytool
    Write-Host "Searching for keytool..." -ForegroundColor Yellow
    $found = Get-ChildItem "C:\Program Files\Java" -Recurse -Name "keytool.exe" -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($found) {
        $keytool = Join-Path "C:\Program Files\Java" $found
    }
}

if ($keytool) {
    Write-Host "✅ Found keytool: $keytool" -ForegroundColor Green
} else {
    Write-Host "❌ keytool not found!" -ForegroundColor Red
    exit 1
}

# Create keystore if needed
Write-Host "Creating keystore if needed..." -ForegroundColor Yellow
if (-not (Test-Path "app\release-key.keystore")) {
    Write-Host "Creating keystore..." -ForegroundColor Cyan
    & $keytool -genkey -v -keystore "app\release-key.keystore" -alias tvcontroller -keyalg RSA -keysize 2048 -validity 10000 -storepass tvcontroller123 -keypass tvcontroller123 -dname "CN=TV Controller, OU=Development, O=TV Controller App, L=City, S=State, C=US"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Keystore created successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to create keystore!" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ Keystore already exists!" -ForegroundColor Green
}

# Check gradlew
Write-Host "Checking gradlew..." -ForegroundColor Yellow
if (Test-Path "gradlew.bat") {
    Write-Host "✅ gradlew.bat found!" -ForegroundColor Green

    # Check gradle-wrapper.jar
    if (-not (Test-Path "gradle\wrapper\gradle-wrapper.jar")) {
        Write-Host "⚠️ gradle-wrapper.jar missing, downloading..." -ForegroundColor Yellow
        New-Item -ItemType Directory -Force -Path "gradle\wrapper" | Out-Null
        try {
            Invoke-WebRequest -Uri "https://github.com/gradle/gradle/raw/v8.4.0/gradle/wrapper/gradle-wrapper.jar" -OutFile "gradle\wrapper\gradle-wrapper.jar"
            Write-Host "✅ gradle-wrapper.jar downloaded!" -ForegroundColor Green
        } catch {
            Write-Host "❌ Failed to download gradle-wrapper.jar!" -ForegroundColor Red
            Write-Host "Please download manually from: https://github.com/gradle/gradle/raw/v8.4.0/gradle/wrapper/gradle-wrapper.jar" -ForegroundColor Yellow
            exit 1
        }
    }
} else {
    Write-Host "❌ gradlew.bat not found!" -ForegroundColor Red
    exit 1
}

# Clean project
Write-Host "Cleaning project..." -ForegroundColor Yellow
& .\gradlew.bat clean
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Clean successful!" -ForegroundColor Green
} else {
    Write-Host "❌ Clean failed!" -ForegroundColor Red
    exit 1
}

# Build APK
Write-Host "Building Release APK..." -ForegroundColor Yellow
& .\gradlew.bat assembleRelease
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Build successful!" -ForegroundColor Green
} else {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    exit 1
}

# Show results
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           BUILD COMPLETE!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "APK files created:" -ForegroundColor Green
Write-Host ""
Write-Host "Release APK:" -ForegroundColor Yellow
Write-Host "app\build\outputs\apk\release\app-release.apk" -ForegroundColor White
Write-Host ""
Write-Host "To install on Android TV:" -ForegroundColor Cyan
Write-Host "1. adb connect [TV_IP]:5555" -ForegroundColor White
Write-Host "2. adb install app\build\outputs\apk\release\app-release.apk" -ForegroundColor White
Write-Host ""

# Check if APK exists
$apkPath = "app\build\outputs\apk\release\app-release.apk"
if (Test-Path $apkPath) {
    $apkSize = (Get-Item $apkPath).Length / 1MB
    Write-Host "✅ APK file size: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Green
} else {
    Write-Host "❌ APK file not found!" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to continue"
