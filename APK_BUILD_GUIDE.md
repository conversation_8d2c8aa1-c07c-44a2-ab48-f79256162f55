# دليل تحويل التطبيق إلى APK

## 🎯 طرق تحويل التطبيق إلى APK

### الطريقة 1: البناء التلقائي (الأسهل) ⭐
```bash
# تشغيل السكريبت التلقائي
build_apk.bat
```

هذا السكريبت سيقوم بـ:
- إنشاء keystore للتوقيع
- تنظيف المشروع
- بناء نسخة Debug
- بناء نسخة Release موقعة

### الطريقة 2: باستخدام Android Studio
1. افتح المشروع في Android Studio
2. انتظر حتى ينتهي Gradle Sync
3. اذهب إلى: **Build > Build Bundle(s) / APK(s) > Build APK(s)**
4. انتظر حتى ينتهي البناء
5. ستجد APK في: `app/build/outputs/apk/`

### الطريقة 3: سطر الأوامر
```bash
# تنظيف المشروع
gradlew clean

# بناء نسخة Debug
gradlew assembleDebug

# بناء نسخة Release
gradlew assembleRelease
```

## 🔐 إعداد التوقيع

### إنشاء Keystore جديد
```bash
keytool -genkey -v -keystore app/release-key.keystore -alias tvcontroller -keyalg RSA -keysize 2048 -validity 10000 -storepass tvcontroller123 -keypass tvcontroller123 -dname "CN=TV Controller, OU=Development, O=TV Controller App, L=City, S=State, C=US"
```

### استخدام Keystore موجود
إذا كان لديك keystore موجود، عدّل في `app/build.gradle`:
```gradle
signingConfigs {
    release {
        storeFile file('path/to/your/keystore.jks')
        storePassword 'your_store_password'
        keyAlias 'your_key_alias'
        keyPassword 'your_key_password'
    }
}
```

## 📁 مواقع ملفات APK

بعد البناء الناجح، ستجد الملفات في:

### نسخة Debug
```
app/build/outputs/apk/debug/app-debug.apk
```
- **الاستخدام**: للاختبار والتطوير
- **التوقيع**: توقيع تطوير تلقائي
- **الحجم**: أكبر (يحتوي على معلومات التشخيص)

### نسخة Release
```
app/build/outputs/apk/release/app-release.apk
```
- **الاستخدام**: للنشر والإنتاج
- **التوقيع**: توقيع إنتاج مخصص
- **الحجم**: أصغر (محسّن ومضغوط)

## 🚀 طرق التثبيت على أجهزة التلفزيون

### 1. ADB عبر USB
```bash
# تمكين خيارات المطور على التلفزيون
# توصيل كابل USB
adb devices
adb install app-release.apk
```

### 2. ADB عبر الشبكة
```bash
# تمكين ADB عبر الشبكة على التلفزيون
adb connect 192.168.1.100:5555
adb install app-release.apk
```

### 3. نقل الملف يدوياً
1. انسخ `app-release.apk` إلى USB
2. أدخل USB في التلفزيون
3. استخدم مدير الملفات لفتح APK
4. اتبع تعليمات التثبيت

### 4. تطبيقات التثبيت
- **ES File Explorer**: مع تمكين "مصادر غير معروفة"
- **Split APKs Installer (SAI)**: للتثبيت المتقدم
- **Package Installer**: المدمج في النظام

## ⚙️ إعدادات البناء المتقدمة

### تخصيص إعدادات البناء
عدّل في `app/build.gradle`:

```gradle
android {
    defaultConfig {
        applicationId "com.yourcompany.tvcontroller"
        versionCode 2
        versionName "1.1"
    }
    
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

### بناء أنواع مختلفة
```bash
# بناء جميع الأنواع
gradlew build

# بناء نوع محدد
gradlew assembleDebug
gradlew assembleRelease

# تنظيف وبناء
gradlew clean assembleRelease
```

## 🔍 فحص APK

### فحص محتويات APK
```bash
# عرض محتويات APK
aapt dump badging app-release.apk

# فحص الصلاحيات
aapt dump permissions app-release.apk

# فحص الموارد
aapt dump resources app-release.apk
```

### فحص التوقيع
```bash
# فحص توقيع APK
jarsigner -verify -verbose -certs app-release.apk

# عرض معلومات الشهادة
keytool -printcert -jarfile app-release.apk
```

## 🐛 حل المشاكل الشائعة

### خطأ: "Keystore not found"
```bash
# تأكد من وجود keystore
ls app/release-key.keystore

# أو أنشئ واحد جديد
create_keystore.bat
```

### خطأ: "Build failed"
```bash
# تنظيف المشروع
gradlew clean

# فحص إعدادات Gradle
gradlew --version

# إعادة تحميل التبعيات
gradlew --refresh-dependencies
```

### خطأ: "Installation failed"
```bash
# تأكد من تمكين "مصادر غير معروفة"
adb shell settings put global install_non_market_apps 1

# أو استخدم force install
adb install -r -d app-release.apk
```

### خطأ: "Insufficient storage"
```bash
# فحص المساحة المتاحة
adb shell df

# تنظيف ذاكرة التخزين المؤقت
adb shell pm clear com.android.packageinstaller
```

## 📊 تحسين حجم APK

### تقليل حجم APK
1. **تمكين ProGuard/R8**:
   ```gradle
   buildTypes {
       release {
           minifyEnabled true
           shrinkResources true
       }
   }
   ```

2. **إزالة الموارد غير المستخدمة**:
   ```gradle
   android {
       buildTypes {
           release {
               shrinkResources true
           }
       }
   }
   ```

3. **ضغط الصور**:
   - استخدم WebP بدلاً من PNG
   - قلل دقة الصور غير الضرورية

### فحص حجم APK
```bash
# عرض حجم APK
ls -lh app-release.apk

# تحليل محتويات APK
gradlew analyzeReleaseBundle
```

## 🎯 نصائح للنشر

### قبل النشر
- [ ] اختبر على أجهزة مختلفة
- [ ] تأكد من عمل جميع الوظائف
- [ ] فحص الأداء والذاكرة
- [ ] مراجعة الصلاحيات المطلوبة

### معلومات النسخة
```gradle
defaultConfig {
    versionCode 1      // رقم النسخة الداخلي
    versionName "1.0"  // رقم النسخة المعروض
}
```

### التوقيع للنشر
- استخدم keystore آمن
- احفظ كلمات المرور بأمان
- لا تشارك keystore مع أحد

---

## 🚀 البدء السريع

للحصول على APK جاهز للتثبيت فوراً:

```bash
# 1. تشغيل البناء التلقائي
build_apk.bat

# 2. أو استخدام الإعداد الشامل
setup_and_deploy.bat

# 3. ستجد APK في:
# app/build/outputs/apk/release/app-release.apk
```

**ملاحظة**: تأكد من تثبيت Java JDK و Android SDK قبل البناء.
