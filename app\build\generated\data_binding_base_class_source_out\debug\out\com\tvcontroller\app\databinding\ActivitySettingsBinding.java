// Generated by view binder compiler. Do not edit!
package com.tvcontroller.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.tvcontroller.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySettingsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnResetDefaults;

  @NonNull
  public final Button btnSave;

  @NonNull
  public final Button btnTestConnection;

  @NonNull
  public final TextInputEditText etReconnectAttempts;

  @NonNull
  public final TextInputEditText etServerIp;

  @NonNull
  public final TextInputEditText etServerPort;

  @NonNull
  public final RadioButton rbArabic;

  @NonNull
  public final RadioButton rbEnglish;

  @NonNull
  public final SwitchCompat switchAutoStart;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvDeviceInfo;

  private ActivitySettingsBinding(@NonNull LinearLayout rootView, @NonNull Button btnCancel,
      @NonNull Button btnResetDefaults, @NonNull Button btnSave, @NonNull Button btnTestConnection,
      @NonNull TextInputEditText etReconnectAttempts, @NonNull TextInputEditText etServerIp,
      @NonNull TextInputEditText etServerPort, @NonNull RadioButton rbArabic,
      @NonNull RadioButton rbEnglish, @NonNull SwitchCompat switchAutoStart,
      @NonNull Toolbar toolbar, @NonNull TextView tvDeviceInfo) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnResetDefaults = btnResetDefaults;
    this.btnSave = btnSave;
    this.btnTestConnection = btnTestConnection;
    this.etReconnectAttempts = etReconnectAttempts;
    this.etServerIp = etServerIp;
    this.etServerPort = etServerPort;
    this.rbArabic = rbArabic;
    this.rbEnglish = rbEnglish;
    this.switchAutoStart = switchAutoStart;
    this.toolbar = toolbar;
    this.tvDeviceInfo = tvDeviceInfo;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_reset_defaults;
      Button btnResetDefaults = ViewBindings.findChildViewById(rootView, id);
      if (btnResetDefaults == null) {
        break missingId;
      }

      id = R.id.btn_save;
      Button btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.btn_test_connection;
      Button btnTestConnection = ViewBindings.findChildViewById(rootView, id);
      if (btnTestConnection == null) {
        break missingId;
      }

      id = R.id.et_reconnect_attempts;
      TextInputEditText etReconnectAttempts = ViewBindings.findChildViewById(rootView, id);
      if (etReconnectAttempts == null) {
        break missingId;
      }

      id = R.id.et_server_ip;
      TextInputEditText etServerIp = ViewBindings.findChildViewById(rootView, id);
      if (etServerIp == null) {
        break missingId;
      }

      id = R.id.et_server_port;
      TextInputEditText etServerPort = ViewBindings.findChildViewById(rootView, id);
      if (etServerPort == null) {
        break missingId;
      }

      id = R.id.rb_arabic;
      RadioButton rbArabic = ViewBindings.findChildViewById(rootView, id);
      if (rbArabic == null) {
        break missingId;
      }

      id = R.id.rb_english;
      RadioButton rbEnglish = ViewBindings.findChildViewById(rootView, id);
      if (rbEnglish == null) {
        break missingId;
      }

      id = R.id.switch_auto_start;
      SwitchCompat switchAutoStart = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoStart == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_device_info;
      TextView tvDeviceInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceInfo == null) {
        break missingId;
      }

      return new ActivitySettingsBinding((LinearLayout) rootView, btnCancel, btnResetDefaults,
          btnSave, btnTestConnection, etReconnectAttempts, etServerIp, etServerPort, rbArabic,
          rbEnglish, switchAutoStart, toolbar, tvDeviceInfo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
