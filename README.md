# تطبيق التحكم في شاشات التلفزيون - TV Controller App

## نظرة عامة
تطبيق Android متقدم للتحكم في شاشات التلفزيون عن بُعد مع إمكانية عرض الشاشة السوداء وإعادة التشغيل التلقائي.

## المميزات الرئيسية
- ✅ عرض شاشة سوداء فوق جميع التطبيقات
- ✅ نصوص متحركة ومؤقت عد تنازلي
- ✅ إعادة تشغيل تلقائي عند استلام أمر من الخادم
- ✅ متوافق مع جميع إصدارات Android (5.0+)
- ✅ تجاوز قيود "خيارات المطور"
- ✅ اتصال عبر WebSocket و TCP
- ✅ خدمة خلفية مستمرة
- ✅ تشغيل تلقائي عند بدء تشغيل الجهاز
- ✅ واجهة إعدادات سهلة الاستخدام

## متطلبات النظام
- Android 5.0 (API 21) أو أحدث
- ذاكرة وصول عشوائي: 1 جيجابايت أو أكثر
- مساحة تخزين: 50 ميجابايت
- اتصال بالشبكة (Wi-Fi أو Ethernet)

## التثبيت

### الطريقة 1: باستخدام ADB (الأسهل)
1. تأكد من تمكين "خيارات المطور" و "تصحيح USB" على جهاز التلفزيون
2. اتصل بالجهاز عبر USB أو الشبكة:
   ```bash
   adb connect [IP_ADDRESS]:5555
   adb install app-release.apk
   ```

### الطريقة 2: التثبيت اليدوي
1. انسخ ملف `app-release.apk` إلى جهاز التلفزيون عبر USB
2. استخدم مدير الملفات لفتح الملف
3. اتبع التعليمات لتثبيت التطبيق

### الطريقة 3: بدون خيارات المطور
1. استخدم تطبيق مثل "Split APKs Installer (SAI)"
2. أو استخدم "ES File Explorer" مع تمكين "مصادر غير معروفة"

## الإعداد الأولي

### 1. منح الصلاحيات
عند تشغيل التطبيق لأول مرة، ستحتاج لمنح الصلاحيات التالية:

#### صلاحية العرض فوق التطبيقات
- انتقل إلى: الإعدادات > التطبيقات > TV Controller > الصلاحيات
- فعّل "العرض فوق التطبيقات الأخرى"

#### إيقاف تحسين البطارية
- انتقل إلى: الإعدادات > البطارية > تحسين البطارية
- ابحث عن "TV Controller" وحدد "عدم التحسين"

### 2. إعداد الخادم
1. افتح التطبيق واضغط على "الإعدادات"
2. أدخل عنوان IP للخادم (مثال: *************)
3. أدخل رقم المنفذ (افتراضي: 8080)
4. اضغط "اختبار الاتصال" للتأكد من الاتصال
5. احفظ الإعدادات

### 3. تشغيل الخدمة
1. ارجع للشاشة الرئيسية
2. اضغط "تشغيل الخدمة"
3. تأكد من ظهور "الخدمة تعمل" باللون الأخضر

## استخدام التطبيق

### أوامر الخادم المدعومة
يمكن للخادم إرسال الأوامر التالية:

```
SHOW_BLACK_SCREEN    - عرض الشاشة السوداء
HIDE_BLACK_SCREEN    - إخفاء الشاشة السوداء
RESTART_APP          - إعادة تشغيل التطبيق
MESSAGE:نص الرسالة   - عرض رسالة مخصصة
COUNTDOWN:30         - بدء عد تنازلي (بالثواني)
```

### مثال على خادم Python بسيط
```python
import socket
import threading

def handle_client(client_socket, address):
    print(f"اتصال من {address}")
    
    while True:
        try:
            # إرسال أمر عرض الشاشة السوداء
            client_socket.send(b"SHOW_BLACK_SCREEN")
            break
        except:
            break
    
    client_socket.close()

def start_server():
    server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server.bind(('0.0.0.0', 8080))
    server.listen(5)
    print("الخادم يعمل على المنفذ 8080...")
    
    while True:
        client_socket, address = server.accept()
        client_thread = threading.Thread(
            target=handle_client, 
            args=(client_socket, address)
        )
        client_thread.start()

if __name__ == "__main__":
    start_server()
```

## بناء التطبيق من المصدر

### المتطلبات
- Android Studio Arctic Fox أو أحدث
- Java JDK 11 أو أحدث
- Android SDK 34

### خطوات البناء
1. استنسخ المشروع أو حمّل الملفات
2. افتح المشروع في Android Studio
3. انتظر حتى ينتهي Gradle من التحميل
4. لبناء APK:
   ```bash
   # بناء نسخة التطوير
   ./gradlew assembleDebug
   
   # بناء نسخة الإنتاج
   ./gradlew assembleRelease
   ```

### بناء سريع باستخدام الملف المجمع
```bash
# على Windows
build_apk.bat

# سيتم إنشاء الملفات في:
# app/build/outputs/apk/debug/app-debug.apk
# app/build/outputs/apk/release/app-release.apk
```

## استكشاف الأخطاء

### التطبيق لا يعمل بعد إعادة التشغيل
- تأكد من منح صلاحية "العرض فوق التطبيقات"
- تأكد من إيقاف تحسين البطارية للتطبيق
- تحقق من تمكين "التشغيل التلقائي" في الإعدادات

### لا يمكن الاتصال بالخادم
- تأكد من أن الخادم يعمل على العنوان والمنفذ المحددين
- تحقق من اتصال الشبكة
- جرب "اختبار الاتصال" من الإعدادات

### الشاشة السوداء لا تظهر
- تأكد من منح صلاحية "العرض فوق التطبيقات"
- أعد تشغيل التطبيق
- تحقق من سجلات النظام باستخدام `adb logcat`

### التطبيق يتوقف عن العمل
- تحقق من ذاكرة الجهاز المتاحة
- أعد تشغيل الجهاز
- أعد تثبيت التطبيق

## الدعم الفني
للحصول على المساعدة أو الإبلاغ عن مشاكل:
- تحقق من سجلات النظام: `adb logcat | grep TVController`
- راجع معلومات الجهاز في الإعدادات
- تأكد من توافق إصدار Android

## الترخيص
هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## دليل التثبيت المفصل لأجهزة التلفزيون المختلفة

### Samsung Smart TV
1. فعّل "وضع المطور":
   - اذهب إلى الإعدادات > عام > إدارة النظام > وضع المطور
   - فعّل "تصحيح USB"
2. ثبّت التطبيق عبر ADB أو نقل الملف

### LG Smart TV (Android TV)
1. اذهب إلى الإعدادات > الجهاز > حول
2. اضغط على "رقم البناء" 7 مرات لتفعيل وضع المطور
3. ارجع للإعدادات > النظام > خيارات المطور
4. فعّل "تصحيح USB"

### Sony Android TV
1. الإعدادات > تفضيلات الجهاز > حول
2. اضغط على "Android TV OS build" 7 مرات
3. ارجع وادخل على "خيارات المطور"
4. فعّل "تصحيح USB"

### Xiaomi Mi TV
1. الإعدادات > حول الجهاز
2. اضغط على "إصدار MIUI TV" 7 مرات
3. ارجع للإعدادات > خيارات المطور
4. فعّل "تصحيح USB"

### TCL Android TV
1. الإعدادات > تفضيلات الجهاز > حول
2. اضغط على "رقم البناء" 7 مرات
3. فعّل "تصحيح USB" من خيارات المطور

## أوامر ADB المفيدة

```bash
# الاتصال بالجهاز
adb connect *************:5555

# التحقق من الاتصال
adb devices

# تثبيت التطبيق
adb install app-release.apk

# تشغيل التطبيق
adb shell am start -n com.tvcontroller.app/.MainActivity

# عرض السجلات
adb logcat | grep TVController

# إعادة تشغيل الجهاز
adb reboot

# نسخ ملف للجهاز
adb push app-release.apk /sdcard/

# حذف التطبيق
adb uninstall com.tvcontroller.app
```

## إعداد الخادم المتقدم

### خادم WebSocket (Node.js)
```javascript
const WebSocket = require('ws');
const wss = new WebSocket.Server({ port: 8080 });

wss.on('connection', function connection(ws) {
    console.log('عميل متصل');

    ws.on('message', function incoming(message) {
        console.log('استلام:', message);
    });

    // إرسال أمر كل 30 ثانية
    setInterval(() => {
        ws.send('SHOW_BLACK_SCREEN');
    }, 30000);
});

console.log('خادم WebSocket يعمل على المنفذ 8080');
```

### خادم TCP (Java)
```java
import java.io.*;
import java.net.*;

public class TVControllerServer {
    public static void main(String[] args) throws IOException {
        ServerSocket serverSocket = new ServerSocket(8080);
        System.out.println("الخادم يعمل على المنفذ 8080");

        while (true) {
            Socket clientSocket = serverSocket.accept();
            new Thread(() -> {
                try {
                    PrintWriter out = new PrintWriter(
                        clientSocket.getOutputStream(), true);

                    // إرسال أمر
                    out.println("SHOW_BLACK_SCREEN");

                    clientSocket.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }).start();
        }
    }
}
```

---
**ملاحظة**: هذا التطبيق مصمم للاستخدام في البيئات المتحكم بها مثل الشاشات التجارية والعروض التقديمية.
