1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tvcontroller.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- Network permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\shasha\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\shasha\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\shasha\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\shasha\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\shasha\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\shasha\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- System overlay permission -->
17    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
17-->C:\shasha\app\src\main\AndroidManifest.xml:11:5-78
17-->C:\shasha\app\src\main\AndroidManifest.xml:11:22-75
18
19    <!-- Foreground service permission -->
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
20-->C:\shasha\app\src\main\AndroidManifest.xml:14:5-77
20-->C:\shasha\app\src\main\AndroidManifest.xml:14:22-74
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED" />
21-->C:\shasha\app\src\main\AndroidManifest.xml:15:5-93
21-->C:\shasha\app\src\main\AndroidManifest.xml:15:22-90
22
23    <!-- Boot receiver permission -->
24    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
24-->C:\shasha\app\src\main\AndroidManifest.xml:18:5-81
24-->C:\shasha\app\src\main\AndroidManifest.xml:18:22-78
25
26    <!-- Wake lock to keep service running -->
27    <uses-permission android:name="android.permission.WAKE_LOCK" />
27-->C:\shasha\app\src\main\AndroidManifest.xml:21:5-68
27-->C:\shasha\app\src\main\AndroidManifest.xml:21:22-65
28
29    <!-- Disable sleep mode -->
30    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
30-->C:\shasha\app\src\main\AndroidManifest.xml:24:5-75
30-->C:\shasha\app\src\main\AndroidManifest.xml:24:22-72
31
32    <!-- Auto-start permission -->
33    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
33-->C:\shasha\app\src\main\AndroidManifest.xml:27:5-95
33-->C:\shasha\app\src\main\AndroidManifest.xml:27:22-92
34
35    <!-- TV compatibility -->
36    <uses-feature
36-->C:\shasha\app\src\main\AndroidManifest.xml:30:5-32:36
37        android:name="android.software.leanback"
37-->C:\shasha\app\src\main\AndroidManifest.xml:31:9-49
38        android:required="false" />
38-->C:\shasha\app\src\main\AndroidManifest.xml:32:9-33
39    <uses-feature
39-->C:\shasha\app\src\main\AndroidManifest.xml:33:5-35:36
40        android:name="android.hardware.touchscreen"
40-->C:\shasha\app\src\main\AndroidManifest.xml:34:9-52
41        android:required="false" />
41-->C:\shasha\app\src\main\AndroidManifest.xml:35:9-33
42
43    <permission
43-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
44        android:name="com.tvcontroller.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.tvcontroller.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
48
49    <application
49-->C:\shasha\app\src\main\AndroidManifest.xml:37:5-100:19
50        android:name="com.tvcontroller.app.TVControllerApplication"
50-->C:\shasha\app\src\main\AndroidManifest.xml:38:9-48
51        android:allowBackup="true"
51-->C:\shasha\app\src\main\AndroidManifest.xml:39:9-35
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
53        android:banner="@mipmap/ic_launcher"
53-->C:\shasha\app\src\main\AndroidManifest.xml:47:9-45
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->C:\shasha\app\src\main\AndroidManifest.xml:40:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="true"
57        android:fullBackupContent="@xml/backup_rules"
57-->C:\shasha\app\src\main\AndroidManifest.xml:41:9-54
58        android:hardwareAccelerated="true"
58-->C:\shasha\app\src\main\AndroidManifest.xml:48:9-43
59        android:icon="@mipmap/ic_launcher"
59-->C:\shasha\app\src\main\AndroidManifest.xml:42:9-43
60        android:label="@string/app_name"
60-->C:\shasha\app\src\main\AndroidManifest.xml:43:9-41
61        android:largeHeap="true"
61-->C:\shasha\app\src\main\AndroidManifest.xml:49:9-33
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->C:\shasha\app\src\main\AndroidManifest.xml:44:9-54
63        android:supportsRtl="true"
63-->C:\shasha\app\src\main\AndroidManifest.xml:45:9-35
64        android:theme="@style/Theme.TVControllerApp" >
64-->C:\shasha\app\src\main\AndroidManifest.xml:46:9-53
65
66        <!-- Main Activity -->
67        <activity
67-->C:\shasha\app\src\main\AndroidManifest.xml:53:9-63:20
68            android:name="com.tvcontroller.app.MainActivity"
68-->C:\shasha\app\src\main\AndroidManifest.xml:54:13-41
69            android:exported="true"
69-->C:\shasha\app\src\main\AndroidManifest.xml:55:13-36
70            android:launchMode="singleTop"
70-->C:\shasha\app\src\main\AndroidManifest.xml:57:13-43
71            android:screenOrientation="landscape" >
71-->C:\shasha\app\src\main\AndroidManifest.xml:56:13-50
72            <intent-filter>
72-->C:\shasha\app\src\main\AndroidManifest.xml:58:13-62:29
73                <action android:name="android.intent.action.MAIN" />
73-->C:\shasha\app\src\main\AndroidManifest.xml:59:17-69
73-->C:\shasha\app\src\main\AndroidManifest.xml:59:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->C:\shasha\app\src\main\AndroidManifest.xml:60:17-77
75-->C:\shasha\app\src\main\AndroidManifest.xml:60:27-74
76                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
76-->C:\shasha\app\src\main\AndroidManifest.xml:61:17-86
76-->C:\shasha\app\src\main\AndroidManifest.xml:61:27-83
77            </intent-filter>
78        </activity>
79
80        <!-- Settings Activity -->
81        <activity
81-->C:\shasha\app\src\main\AndroidManifest.xml:66:9-70:58
82            android:name="com.tvcontroller.app.SettingsActivity"
82-->C:\shasha\app\src\main\AndroidManifest.xml:67:13-45
83            android:exported="false"
83-->C:\shasha\app\src\main\AndroidManifest.xml:68:13-37
84            android:parentActivityName="com.tvcontroller.app.MainActivity"
84-->C:\shasha\app\src\main\AndroidManifest.xml:70:13-55
85            android:screenOrientation="landscape" />
85-->C:\shasha\app\src\main\AndroidManifest.xml:69:13-50
86
87        <!-- Overlay Service -->
88        <service
88-->C:\shasha\app\src\main\AndroidManifest.xml:73:9-77:62
89            android:name="com.tvcontroller.app.service.OverlayService"
89-->C:\shasha\app\src\main\AndroidManifest.xml:74:13-51
90            android:enabled="true"
90-->C:\shasha\app\src\main\AndroidManifest.xml:75:13-35
91            android:exported="false"
91-->C:\shasha\app\src\main\AndroidManifest.xml:76:13-37
92            android:foregroundServiceType="systemExempted" />
92-->C:\shasha\app\src\main\AndroidManifest.xml:77:13-59
93
94        <!-- Boot Receiver -->
95        <receiver
95-->C:\shasha\app\src\main\AndroidManifest.xml:80:9-92:20
96            android:name="com.tvcontroller.app.receiver.BootReceiver"
96-->C:\shasha\app\src\main\AndroidManifest.xml:81:13-50
97            android:enabled="true"
97-->C:\shasha\app\src\main\AndroidManifest.xml:82:13-35
98            android:exported="true" >
98-->C:\shasha\app\src\main\AndroidManifest.xml:83:13-36
99            <intent-filter android:priority="1000" >
99-->C:\shasha\app\src\main\AndroidManifest.xml:84:13-91:29
99-->C:\shasha\app\src\main\AndroidManifest.xml:84:28-51
100                <action android:name="android.intent.action.BOOT_COMPLETED" />
100-->C:\shasha\app\src\main\AndroidManifest.xml:85:17-79
100-->C:\shasha\app\src\main\AndroidManifest.xml:85:25-76
101                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
101-->C:\shasha\app\src\main\AndroidManifest.xml:86:17-82
101-->C:\shasha\app\src\main\AndroidManifest.xml:86:25-79
102                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
102-->C:\shasha\app\src\main\AndroidManifest.xml:87:17-82
102-->C:\shasha\app\src\main\AndroidManifest.xml:87:25-79
103                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
103-->C:\shasha\app\src\main\AndroidManifest.xml:88:17-84
103-->C:\shasha\app\src\main\AndroidManifest.xml:88:25-81
104                <action android:name="android.intent.action.PACKAGE_REPLACED" />
104-->C:\shasha\app\src\main\AndroidManifest.xml:89:17-81
104-->C:\shasha\app\src\main\AndroidManifest.xml:89:25-78
105
106                <data android:scheme="package" />
106-->C:\shasha\app\src\main\AndroidManifest.xml:90:17-50
106-->C:\shasha\app\src\main\AndroidManifest.xml:90:23-47
107            </intent-filter>
108        </receiver>
109
110        <!-- Auto-restart Receiver -->
111        <receiver
111-->C:\shasha\app\src\main\AndroidManifest.xml:95:9-98:40
112            android:name="com.tvcontroller.app.receiver.RestartReceiver"
112-->C:\shasha\app\src\main\AndroidManifest.xml:96:13-53
113            android:enabled="true"
113-->C:\shasha\app\src\main\AndroidManifest.xml:97:13-35
114            android:exported="false" />
114-->C:\shasha\app\src\main\AndroidManifest.xml:98:13-37
115
116        <provider
116-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
117            android:name="androidx.startup.InitializationProvider"
117-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
118            android:authorities="com.tvcontroller.app.debug.androidx-startup"
118-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
119            android:exported="false" >
119-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
120            <meta-data
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
121                android:name="androidx.work.WorkManagerInitializer"
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
122                android:value="androidx.startup" />
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
123            <meta-data
123-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
124                android:name="androidx.emoji2.text.EmojiCompatInitializer"
124-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
125                android:value="androidx.startup" />
125-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
126            <meta-data
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0812a15fbf0951e94b9850804d37742b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0812a15fbf0951e94b9850804d37742b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
128                android:value="androidx.startup" />
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0812a15fbf0951e94b9850804d37742b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
131                android:value="androidx.startup" />
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
132        </provider>
133
134        <service
134-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
135            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
135-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
136            android:directBootAware="false"
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
137            android:enabled="@bool/enable_system_alarm_service_default"
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
138            android:exported="false" />
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
139        <service
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
140            android:name="androidx.work.impl.background.systemjob.SystemJobService"
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
141            android:directBootAware="false"
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
142            android:enabled="@bool/enable_system_job_service_default"
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
143            android:exported="true"
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
144            android:permission="android.permission.BIND_JOB_SERVICE" />
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
145        <service
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
146            android:name="androidx.work.impl.foreground.SystemForegroundService"
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
147            android:directBootAware="false"
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
148            android:enabled="@bool/enable_system_foreground_service_default"
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
149            android:exported="false" />
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
150
151        <receiver
151-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
152            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
153            android:directBootAware="false"
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
154            android:enabled="true"
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
155            android:exported="false" />
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
156        <receiver
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
157            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
157-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
158            android:directBootAware="false"
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
159            android:enabled="false"
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
160            android:exported="false" >
160-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
161            <intent-filter>
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
162                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
163                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
164            </intent-filter>
165        </receiver>
166        <receiver
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
167            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
168            android:directBootAware="false"
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
169            android:enabled="false"
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
170            android:exported="false" >
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
171            <intent-filter>
171-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
172                <action android:name="android.intent.action.BATTERY_OKAY" />
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
173                <action android:name="android.intent.action.BATTERY_LOW" />
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
174            </intent-filter>
175        </receiver>
176        <receiver
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
177            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
177-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
179            android:enabled="false"
179-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
180            android:exported="false" >
180-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
181            <intent-filter>
181-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
182                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
183                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
184            </intent-filter>
185        </receiver>
186        <receiver
186-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
187            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
188            android:directBootAware="false"
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
189            android:enabled="false"
189-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
190            android:exported="false" >
190-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
191            <intent-filter>
191-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
192                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
192-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
192-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
193            </intent-filter>
194        </receiver>
195        <receiver
195-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
196            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
196-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
197            android:directBootAware="false"
197-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
198            android:enabled="false"
198-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
199            android:exported="false" >
199-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
200            <intent-filter>
200-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
201                <action android:name="android.intent.action.BOOT_COMPLETED" />
201-->C:\shasha\app\src\main\AndroidManifest.xml:85:17-79
201-->C:\shasha\app\src\main\AndroidManifest.xml:85:25-76
202                <action android:name="android.intent.action.TIME_SET" />
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
203                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
204            </intent-filter>
205        </receiver>
206        <receiver
206-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
207            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
208            android:directBootAware="false"
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
209            android:enabled="@bool/enable_system_alarm_service_default"
209-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
210            android:exported="false" >
210-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
211            <intent-filter>
211-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
212                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
213            </intent-filter>
214        </receiver>
215        <receiver
215-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
216            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
216-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
217            android:directBootAware="false"
217-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
218            android:enabled="true"
218-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
219            android:exported="true"
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
220            android:permission="android.permission.DUMP" >
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
221            <intent-filter>
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
222                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb62c0cc60c2f6401994d9371d3db0e3\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
223            </intent-filter>
224        </receiver>
225
226        <uses-library
226-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d18da7628f1e260f193ca4577fef5aee\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
227            android:name="androidx.window.extensions"
227-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d18da7628f1e260f193ca4577fef5aee\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
228            android:required="false" />
228-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d18da7628f1e260f193ca4577fef5aee\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
229        <uses-library
229-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d18da7628f1e260f193ca4577fef5aee\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
230            android:name="androidx.window.sidecar"
230-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d18da7628f1e260f193ca4577fef5aee\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
231            android:required="false" />
231-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d18da7628f1e260f193ca4577fef5aee\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
232
233        <service
233-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\23a2c2c301c20852c053322d0380e3d0\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
234            android:name="androidx.room.MultiInstanceInvalidationService"
234-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\23a2c2c301c20852c053322d0380e3d0\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
235            android:directBootAware="true"
235-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\23a2c2c301c20852c053322d0380e3d0\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
236            android:exported="false" />
236-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\23a2c2c301c20852c053322d0380e3d0\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
237
238        <receiver
238-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
239            android:name="androidx.profileinstaller.ProfileInstallReceiver"
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
240            android:directBootAware="false"
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
241            android:enabled="true"
241-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
242            android:exported="true"
242-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
243            android:permission="android.permission.DUMP" >
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
244            <intent-filter>
244-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
245                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
245-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
245-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
246            </intent-filter>
247            <intent-filter>
247-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
248                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
248-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
248-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
249            </intent-filter>
250            <intent-filter>
250-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
251                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
251-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
251-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
252            </intent-filter>
253            <intent-filter>
253-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
254                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
254-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
254-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
255            </intent-filter>
256        </receiver>
257    </application>
258
259</manifest>
