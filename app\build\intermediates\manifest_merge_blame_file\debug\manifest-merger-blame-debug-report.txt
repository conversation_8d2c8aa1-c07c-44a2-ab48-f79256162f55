1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tvcontroller.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- Network permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\shasha\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\shasha\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\shasha\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\shasha\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\shasha\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\shasha\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- System overlay permission -->
17    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
17-->C:\shasha\app\src\main\AndroidManifest.xml:11:5-78
17-->C:\shasha\app\src\main\AndroidManifest.xml:11:22-75
18
19    <!-- Foreground service permission -->
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
20-->C:\shasha\app\src\main\AndroidManifest.xml:14:5-77
20-->C:\shasha\app\src\main\AndroidManifest.xml:14:22-74
21
22    <!-- Boot receiver permission -->
23    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
23-->C:\shasha\app\src\main\AndroidManifest.xml:17:5-81
23-->C:\shasha\app\src\main\AndroidManifest.xml:17:22-78
24
25    <!-- Wake lock to keep service running -->
26    <uses-permission android:name="android.permission.WAKE_LOCK" />
26-->C:\shasha\app\src\main\AndroidManifest.xml:20:5-68
26-->C:\shasha\app\src\main\AndroidManifest.xml:20:22-65
27
28    <!-- Disable sleep mode -->
29    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
29-->C:\shasha\app\src\main\AndroidManifest.xml:23:5-75
29-->C:\shasha\app\src\main\AndroidManifest.xml:23:22-72
30
31    <!-- Auto-start permission -->
32    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
32-->C:\shasha\app\src\main\AndroidManifest.xml:26:5-95
32-->C:\shasha\app\src\main\AndroidManifest.xml:26:22-92
33
34    <!-- TV compatibility -->
35    <uses-feature
35-->C:\shasha\app\src\main\AndroidManifest.xml:29:5-31:36
36        android:name="android.software.leanback"
36-->C:\shasha\app\src\main\AndroidManifest.xml:30:9-49
37        android:required="false" />
37-->C:\shasha\app\src\main\AndroidManifest.xml:31:9-33
38    <uses-feature
38-->C:\shasha\app\src\main\AndroidManifest.xml:32:5-34:36
39        android:name="android.hardware.touchscreen"
39-->C:\shasha\app\src\main\AndroidManifest.xml:33:9-52
40        android:required="false" />
40-->C:\shasha\app\src\main\AndroidManifest.xml:34:9-33
41
42    <permission
42-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
43        android:name="com.tvcontroller.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
43-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
44        android:protectionLevel="signature" />
44-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
45
46    <uses-permission android:name="com.tvcontroller.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
46-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
46-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
47
48    <application
48-->C:\shasha\app\src\main\AndroidManifest.xml:36:5-98:19
49        android:name="com.tvcontroller.app.TVControllerApplication"
49-->C:\shasha\app\src\main\AndroidManifest.xml:37:9-48
50        android:allowBackup="true"
50-->C:\shasha\app\src\main\AndroidManifest.xml:38:9-35
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
52        android:banner="@mipmap/ic_launcher"
52-->C:\shasha\app\src\main\AndroidManifest.xml:46:9-45
53        android:dataExtractionRules="@xml/data_extraction_rules"
53-->C:\shasha\app\src\main\AndroidManifest.xml:39:9-65
54        android:debuggable="true"
55        android:extractNativeLibs="true"
56        android:fullBackupContent="@xml/backup_rules"
56-->C:\shasha\app\src\main\AndroidManifest.xml:40:9-54
57        android:hardwareAccelerated="true"
57-->C:\shasha\app\src\main\AndroidManifest.xml:47:9-43
58        android:icon="@mipmap/ic_launcher"
58-->C:\shasha\app\src\main\AndroidManifest.xml:41:9-43
59        android:label="@string/app_name"
59-->C:\shasha\app\src\main\AndroidManifest.xml:42:9-41
60        android:largeHeap="true"
60-->C:\shasha\app\src\main\AndroidManifest.xml:48:9-33
61        android:roundIcon="@mipmap/ic_launcher_round"
61-->C:\shasha\app\src\main\AndroidManifest.xml:43:9-54
62        android:supportsRtl="true"
62-->C:\shasha\app\src\main\AndroidManifest.xml:44:9-35
63        android:theme="@style/Theme.TVControllerApp" >
63-->C:\shasha\app\src\main\AndroidManifest.xml:45:9-53
64
65        <!-- Main Activity -->
66        <activity
66-->C:\shasha\app\src\main\AndroidManifest.xml:52:9-62:20
67            android:name="com.tvcontroller.app.MainActivity"
67-->C:\shasha\app\src\main\AndroidManifest.xml:53:13-41
68            android:exported="true"
68-->C:\shasha\app\src\main\AndroidManifest.xml:54:13-36
69            android:launchMode="singleTop"
69-->C:\shasha\app\src\main\AndroidManifest.xml:56:13-43
70            android:screenOrientation="landscape" >
70-->C:\shasha\app\src\main\AndroidManifest.xml:55:13-50
71            <intent-filter>
71-->C:\shasha\app\src\main\AndroidManifest.xml:57:13-61:29
72                <action android:name="android.intent.action.MAIN" />
72-->C:\shasha\app\src\main\AndroidManifest.xml:58:17-69
72-->C:\shasha\app\src\main\AndroidManifest.xml:58:25-66
73
74                <category android:name="android.intent.category.LAUNCHER" />
74-->C:\shasha\app\src\main\AndroidManifest.xml:59:17-77
74-->C:\shasha\app\src\main\AndroidManifest.xml:59:27-74
75                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
75-->C:\shasha\app\src\main\AndroidManifest.xml:60:17-86
75-->C:\shasha\app\src\main\AndroidManifest.xml:60:27-83
76            </intent-filter>
77        </activity>
78
79        <!-- Settings Activity -->
80        <activity
80-->C:\shasha\app\src\main\AndroidManifest.xml:65:9-69:58
81            android:name="com.tvcontroller.app.SettingsActivity"
81-->C:\shasha\app\src\main\AndroidManifest.xml:66:13-45
82            android:exported="false"
82-->C:\shasha\app\src\main\AndroidManifest.xml:67:13-37
83            android:parentActivityName="com.tvcontroller.app.MainActivity"
83-->C:\shasha\app\src\main\AndroidManifest.xml:69:13-55
84            android:screenOrientation="landscape" />
84-->C:\shasha\app\src\main\AndroidManifest.xml:68:13-50
85
86        <!-- Overlay Service -->
87        <service
87-->C:\shasha\app\src\main\AndroidManifest.xml:72:9-75:40
88            android:name="com.tvcontroller.app.service.OverlayService"
88-->C:\shasha\app\src\main\AndroidManifest.xml:73:13-51
89            android:enabled="true"
89-->C:\shasha\app\src\main\AndroidManifest.xml:74:13-35
90            android:exported="false" />
90-->C:\shasha\app\src\main\AndroidManifest.xml:75:13-37
91
92        <!-- Boot Receiver -->
93        <receiver
93-->C:\shasha\app\src\main\AndroidManifest.xml:78:9-90:20
94            android:name="com.tvcontroller.app.receiver.BootReceiver"
94-->C:\shasha\app\src\main\AndroidManifest.xml:79:13-50
95            android:enabled="true"
95-->C:\shasha\app\src\main\AndroidManifest.xml:80:13-35
96            android:exported="true" >
96-->C:\shasha\app\src\main\AndroidManifest.xml:81:13-36
97            <intent-filter android:priority="1000" >
97-->C:\shasha\app\src\main\AndroidManifest.xml:82:13-89:29
97-->C:\shasha\app\src\main\AndroidManifest.xml:82:28-51
98                <action android:name="android.intent.action.BOOT_COMPLETED" />
98-->C:\shasha\app\src\main\AndroidManifest.xml:83:17-79
98-->C:\shasha\app\src\main\AndroidManifest.xml:83:25-76
99                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
99-->C:\shasha\app\src\main\AndroidManifest.xml:84:17-82
99-->C:\shasha\app\src\main\AndroidManifest.xml:84:25-79
100                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
100-->C:\shasha\app\src\main\AndroidManifest.xml:85:17-82
100-->C:\shasha\app\src\main\AndroidManifest.xml:85:25-79
101                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
101-->C:\shasha\app\src\main\AndroidManifest.xml:86:17-84
101-->C:\shasha\app\src\main\AndroidManifest.xml:86:25-81
102                <action android:name="android.intent.action.PACKAGE_REPLACED" />
102-->C:\shasha\app\src\main\AndroidManifest.xml:87:17-81
102-->C:\shasha\app\src\main\AndroidManifest.xml:87:25-78
103
104                <data android:scheme="package" />
104-->C:\shasha\app\src\main\AndroidManifest.xml:88:17-50
104-->C:\shasha\app\src\main\AndroidManifest.xml:88:23-47
105            </intent-filter>
106        </receiver>
107
108        <!-- Auto-restart Receiver -->
109        <receiver
109-->C:\shasha\app\src\main\AndroidManifest.xml:93:9-96:40
110            android:name="com.tvcontroller.app.receiver.RestartReceiver"
110-->C:\shasha\app\src\main\AndroidManifest.xml:94:13-53
111            android:enabled="true"
111-->C:\shasha\app\src\main\AndroidManifest.xml:95:13-35
112            android:exported="false" />
112-->C:\shasha\app\src\main\AndroidManifest.xml:96:13-37
113
114        <provider
114-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
115            android:name="androidx.startup.InitializationProvider"
115-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
116            android:authorities="com.tvcontroller.app.androidx-startup"
116-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
117            android:exported="false" >
117-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
118            <meta-data
118-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
119                android:name="androidx.emoji2.text.EmojiCompatInitializer"
119-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
120                android:value="androidx.startup" />
120-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
121            <meta-data
121-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0812a15fbf0951e94b9850804d37742b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
122-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0812a15fbf0951e94b9850804d37742b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
123                android:value="androidx.startup" />
123-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0812a15fbf0951e94b9850804d37742b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
124            <meta-data
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
126                android:value="androidx.startup" />
126-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
127        </provider>
128
129        <receiver
129-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
130            android:name="androidx.profileinstaller.ProfileInstallReceiver"
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
131            android:directBootAware="false"
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
132            android:enabled="true"
132-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
133            android:exported="true"
133-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
134            android:permission="android.permission.DUMP" >
134-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
136                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
139                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
139-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
142                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
142-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
142-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
143            </intent-filter>
144            <intent-filter>
144-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
145                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
145-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
145-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
146            </intent-filter>
147        </receiver>
148    </application>
149
150</manifest>
