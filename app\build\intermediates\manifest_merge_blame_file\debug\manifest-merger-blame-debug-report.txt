1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tvcontroller.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- Network permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\shasha\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\shasha\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\shasha\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\shasha\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\shasha\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\shasha\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- System overlay permission -->
17    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
17-->C:\shasha\app\src\main\AndroidManifest.xml:11:5-78
17-->C:\shasha\app\src\main\AndroidManifest.xml:11:22-75
18
19    <!-- Foreground service permission -->
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
20-->C:\shasha\app\src\main\AndroidManifest.xml:14:5-77
20-->C:\shasha\app\src\main\AndroidManifest.xml:14:22-74
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED" />
21-->C:\shasha\app\src\main\AndroidManifest.xml:15:5-93
21-->C:\shasha\app\src\main\AndroidManifest.xml:15:22-90
22
23    <!-- Boot receiver permission -->
24    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
24-->C:\shasha\app\src\main\AndroidManifest.xml:18:5-81
24-->C:\shasha\app\src\main\AndroidManifest.xml:18:22-78
25
26    <!-- Wake lock to keep service running -->
27    <uses-permission android:name="android.permission.WAKE_LOCK" />
27-->C:\shasha\app\src\main\AndroidManifest.xml:21:5-68
27-->C:\shasha\app\src\main\AndroidManifest.xml:21:22-65
28
29    <!-- Disable sleep mode -->
30    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
30-->C:\shasha\app\src\main\AndroidManifest.xml:24:5-75
30-->C:\shasha\app\src\main\AndroidManifest.xml:24:22-72
31
32    <!-- Auto-start permission -->
33    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
33-->C:\shasha\app\src\main\AndroidManifest.xml:27:5-95
33-->C:\shasha\app\src\main\AndroidManifest.xml:27:22-92
34
35    <!-- TV compatibility -->
36    <uses-feature
36-->C:\shasha\app\src\main\AndroidManifest.xml:30:5-32:36
37        android:name="android.software.leanback"
37-->C:\shasha\app\src\main\AndroidManifest.xml:31:9-49
38        android:required="false" />
38-->C:\shasha\app\src\main\AndroidManifest.xml:32:9-33
39    <uses-feature
39-->C:\shasha\app\src\main\AndroidManifest.xml:33:5-35:36
40        android:name="android.hardware.touchscreen"
40-->C:\shasha\app\src\main\AndroidManifest.xml:34:9-52
41        android:required="false" />
41-->C:\shasha\app\src\main\AndroidManifest.xml:35:9-33
42
43    <permission
43-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
44        android:name="com.tvcontroller.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.tvcontroller.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
48
49    <application
49-->C:\shasha\app\src\main\AndroidManifest.xml:37:5-100:19
50        android:name="com.tvcontroller.app.TVControllerApplication"
50-->C:\shasha\app\src\main\AndroidManifest.xml:38:9-48
51        android:allowBackup="true"
51-->C:\shasha\app\src\main\AndroidManifest.xml:39:9-35
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6274a1892fdd5def28bdd9767110ee\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
53        android:banner="@mipmap/ic_launcher"
53-->C:\shasha\app\src\main\AndroidManifest.xml:47:9-45
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->C:\shasha\app\src\main\AndroidManifest.xml:40:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="true"
57        android:fullBackupContent="@xml/backup_rules"
57-->C:\shasha\app\src\main\AndroidManifest.xml:41:9-54
58        android:hardwareAccelerated="true"
58-->C:\shasha\app\src\main\AndroidManifest.xml:48:9-43
59        android:icon="@mipmap/ic_launcher"
59-->C:\shasha\app\src\main\AndroidManifest.xml:42:9-43
60        android:label="@string/app_name"
60-->C:\shasha\app\src\main\AndroidManifest.xml:43:9-41
61        android:largeHeap="true"
61-->C:\shasha\app\src\main\AndroidManifest.xml:49:9-33
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->C:\shasha\app\src\main\AndroidManifest.xml:44:9-54
63        android:supportsRtl="true"
63-->C:\shasha\app\src\main\AndroidManifest.xml:45:9-35
64        android:theme="@style/Theme.TVControllerApp" >
64-->C:\shasha\app\src\main\AndroidManifest.xml:46:9-53
65
66        <!-- Main Activity -->
67        <activity
67-->C:\shasha\app\src\main\AndroidManifest.xml:53:9-63:20
68            android:name="com.tvcontroller.app.MainActivity"
68-->C:\shasha\app\src\main\AndroidManifest.xml:54:13-41
69            android:exported="true"
69-->C:\shasha\app\src\main\AndroidManifest.xml:55:13-36
70            android:launchMode="singleTop"
70-->C:\shasha\app\src\main\AndroidManifest.xml:57:13-43
71            android:screenOrientation="landscape" >
71-->C:\shasha\app\src\main\AndroidManifest.xml:56:13-50
72            <intent-filter>
72-->C:\shasha\app\src\main\AndroidManifest.xml:58:13-62:29
73                <action android:name="android.intent.action.MAIN" />
73-->C:\shasha\app\src\main\AndroidManifest.xml:59:17-69
73-->C:\shasha\app\src\main\AndroidManifest.xml:59:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->C:\shasha\app\src\main\AndroidManifest.xml:60:17-77
75-->C:\shasha\app\src\main\AndroidManifest.xml:60:27-74
76                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
76-->C:\shasha\app\src\main\AndroidManifest.xml:61:17-86
76-->C:\shasha\app\src\main\AndroidManifest.xml:61:27-83
77            </intent-filter>
78        </activity>
79
80        <!-- Settings Activity -->
81        <activity
81-->C:\shasha\app\src\main\AndroidManifest.xml:66:9-70:58
82            android:name="com.tvcontroller.app.SettingsActivity"
82-->C:\shasha\app\src\main\AndroidManifest.xml:67:13-45
83            android:exported="false"
83-->C:\shasha\app\src\main\AndroidManifest.xml:68:13-37
84            android:parentActivityName="com.tvcontroller.app.MainActivity"
84-->C:\shasha\app\src\main\AndroidManifest.xml:70:13-55
85            android:screenOrientation="landscape" />
85-->C:\shasha\app\src\main\AndroidManifest.xml:69:13-50
86
87        <!-- Overlay Service -->
88        <service
88-->C:\shasha\app\src\main\AndroidManifest.xml:73:9-77:62
89            android:name="com.tvcontroller.app.service.OverlayService"
89-->C:\shasha\app\src\main\AndroidManifest.xml:74:13-51
90            android:enabled="true"
90-->C:\shasha\app\src\main\AndroidManifest.xml:75:13-35
91            android:exported="false"
91-->C:\shasha\app\src\main\AndroidManifest.xml:76:13-37
92            android:foregroundServiceType="systemExempted" />
92-->C:\shasha\app\src\main\AndroidManifest.xml:77:13-59
93
94        <!-- Boot Receiver -->
95        <receiver
95-->C:\shasha\app\src\main\AndroidManifest.xml:80:9-92:20
96            android:name="com.tvcontroller.app.receiver.BootReceiver"
96-->C:\shasha\app\src\main\AndroidManifest.xml:81:13-50
97            android:enabled="true"
97-->C:\shasha\app\src\main\AndroidManifest.xml:82:13-35
98            android:exported="true" >
98-->C:\shasha\app\src\main\AndroidManifest.xml:83:13-36
99            <intent-filter android:priority="1000" >
99-->C:\shasha\app\src\main\AndroidManifest.xml:84:13-91:29
99-->C:\shasha\app\src\main\AndroidManifest.xml:84:28-51
100                <action android:name="android.intent.action.BOOT_COMPLETED" />
100-->C:\shasha\app\src\main\AndroidManifest.xml:85:17-79
100-->C:\shasha\app\src\main\AndroidManifest.xml:85:25-76
101                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
101-->C:\shasha\app\src\main\AndroidManifest.xml:86:17-82
101-->C:\shasha\app\src\main\AndroidManifest.xml:86:25-79
102                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
102-->C:\shasha\app\src\main\AndroidManifest.xml:87:17-82
102-->C:\shasha\app\src\main\AndroidManifest.xml:87:25-79
103                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
103-->C:\shasha\app\src\main\AndroidManifest.xml:88:17-84
103-->C:\shasha\app\src\main\AndroidManifest.xml:88:25-81
104                <action android:name="android.intent.action.PACKAGE_REPLACED" />
104-->C:\shasha\app\src\main\AndroidManifest.xml:89:17-81
104-->C:\shasha\app\src\main\AndroidManifest.xml:89:25-78
105
106                <data android:scheme="package" />
106-->C:\shasha\app\src\main\AndroidManifest.xml:90:17-50
106-->C:\shasha\app\src\main\AndroidManifest.xml:90:23-47
107            </intent-filter>
108        </receiver>
109
110        <!-- Auto-restart Receiver -->
111        <receiver
111-->C:\shasha\app\src\main\AndroidManifest.xml:95:9-98:40
112            android:name="com.tvcontroller.app.receiver.RestartReceiver"
112-->C:\shasha\app\src\main\AndroidManifest.xml:96:13-53
113            android:enabled="true"
113-->C:\shasha\app\src\main\AndroidManifest.xml:97:13-35
114            android:exported="false" />
114-->C:\shasha\app\src\main\AndroidManifest.xml:98:13-37
115
116        <provider
116-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
117            android:name="androidx.startup.InitializationProvider"
117-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
118            android:authorities="com.tvcontroller.app.androidx-startup"
118-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
119            android:exported="false" >
119-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
120            <meta-data
120-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
121                android:name="androidx.emoji2.text.EmojiCompatInitializer"
121-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
122                android:value="androidx.startup" />
122-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9d28c5d1ecb56f0dc028519e2164d79\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
123            <meta-data
123-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0812a15fbf0951e94b9850804d37742b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
124                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
124-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0812a15fbf0951e94b9850804d37742b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
125                android:value="androidx.startup" />
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0812a15fbf0951e94b9850804d37742b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
126            <meta-data
126-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
128                android:value="androidx.startup" />
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
129        </provider>
130
131        <receiver
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
132            android:name="androidx.profileinstaller.ProfileInstallReceiver"
132-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
133            android:directBootAware="false"
133-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
134            android:enabled="true"
134-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
135            android:exported="true"
135-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
136            android:permission="android.permission.DUMP" >
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
137            <intent-filter>
137-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
138                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
138-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
138-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
139            </intent-filter>
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
141                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
141-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
141-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
142            </intent-filter>
143            <intent-filter>
143-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
144                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
144-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
144-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
145            </intent-filter>
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
147                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
147-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
147-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\18b6739198305ab58430125b0f3c02d3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
148            </intent-filter>
149        </receiver>
150    </application>
151
152</manifest>
