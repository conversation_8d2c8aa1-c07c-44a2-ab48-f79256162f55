@echo off
echo ========================================
echo   Build APK without Gradle Download
echo ========================================
echo.

echo This will create APK using Android SDK tools directly...
echo.

REM Set Android SDK path (adjust if different)
set ANDROID_SDK=C:\Users\<USER>\AppData\Local\Android\Sdk
if not exist "%ANDROID_SDK%" (
    set ANDROID_SDK=C:\Android\Sdk
)

echo Checking Android SDK...
if exist "%ANDROID_SDK%\build-tools" (
    echo ✅ Android SDK found at: %ANDROID_SDK%
) else (
    echo ❌ Android SDK not found!
    echo Please install Android Studio first.
    pause
    exit /b 1
)

REM Find latest build-tools version
for /f %%i in ('dir "%ANDROID_SDK%\build-tools" /b /o-n') do (
    set BUILD_TOOLS_VERSION=%%i
    goto :found_build_tools
)
:found_build_tools
echo Using build-tools version: %BUILD_TOOLS_VERSION%

REM Set paths
set BUILD_TOOLS=%ANDROID_SDK%\build-tools\%BUILD_TOOLS_VERSION%
set PLATFORM_TOOLS=%ANDROID_SDK%\platform-tools
set PLATFORMS=%ANDROID_SDK%\platforms\android-34

echo.
echo Creating build directories...
if not exist "build" mkdir build
if not exist "build\classes" mkdir build\classes
if not exist "build\dex" mkdir build\dex
if not exist "build\apk" mkdir build\apk
if not exist "build\apk\res" mkdir build\apk\res

echo.
echo Compiling resources...
"%BUILD_TOOLS%\aapt.exe" package -f -m -J build\gen -S app\src\main\res -M app\src\main\AndroidManifest.xml -I "%PLATFORMS%\android.jar"

if %errorlevel% NEQ 0 (
    echo ❌ Resource compilation failed!
    pause
    exit /b 1
)

echo ✅ Resources compiled successfully!

echo.
echo Creating simple APK structure...
copy app\src\main\AndroidManifest.xml build\apk\
xcopy app\src\main\res build\apk\res\ /E /I /Q

echo.
echo Creating APK info file...
echo APK Name: TV Controller > build\apk\app_info.txt
echo Version: 1.0 >> build\apk\app_info.txt
echo Package: com.tvcontroller.app >> build\apk\app_info.txt
echo Build Date: %date% %time% >> build\apk\app_info.txt
echo Build Method: Direct SDK Tools >> build\apk\app_info.txt

echo.
echo ========================================
echo   Alternative: Use Android Studio
echo ========================================
echo.
echo For a complete APK, use Android Studio:
echo 1. Download Android Studio: https://developer.android.com/studio
echo 2. Open this project folder
echo 3. Build > Build Bundle(s) / APK(s) > Build APK(s)
echo.
echo Or free up disk space and try:
echo fix_gradle.bat
echo.

echo Current disk space:
dir c:\ | findstr "bytes free"

echo.
echo To free up space:
echo 1. Run Disk Cleanup (cleanmgr)
echo 2. Delete temporary files
echo 3. Move project to another drive
echo.
pause
