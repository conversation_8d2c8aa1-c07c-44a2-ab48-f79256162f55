plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.tvcontroller.app'
    compileSdk 34

    defaultConfig {
        applicationId "com.tvcontroller.app"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // Enable vector drawable support for older versions
        vectorDrawables.useSupportLibrary = true
    }

    signingConfigs {
        release {
            storeFile file('release-key.keystore')
            storePassword 'tvcontroller123'
            keyAlias 'tvcontroller'
            keyPassword 'tvcontroller123'
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }

        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release

            // Optimize APK
            zipAlignEnabled true

            // Enable R8 full mode
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    kotlinOptions {
        jvmTarget = '1.8'
    }
    
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    
    // WebSocket and HTTP client
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // WorkManager for background tasks
    implementation 'androidx.work:work-runtime-ktx:2.9.0'
    
    // Lifecycle components
    implementation 'androidx.lifecycle:lifecycle-service:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-process:2.7.0'
    
    // Gson for JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // Preferences
    implementation 'androidx.preference:preference-ktx:1.2.1'
    
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
