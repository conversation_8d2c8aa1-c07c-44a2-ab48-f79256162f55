<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_color">#FFFF5722</color>
    <color name="black">#FF000000</color>
    <color name="blue">#FF0000FF</color>
    <color name="green">#FF00FF00</color>
    <color name="orange">#FFFF8000</color>
    <color name="overlay_background">#FF000000</color>
    <color name="overlay_progress">#FFFF5722</color>
    <color name="overlay_text">#FFFFFFFF</color>
    <color name="primary_color">#FF2196F3</color>
    <color name="primary_dark_color">#FF1976D2</color>
    <color name="purple">#FF800080</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="red">#FFFF0000</color>
    <color name="semi_transparent">#80000000</color>
    <color name="status_connected">#FF4CAF50</color>
    <color name="status_connecting">#FFFF9800</color>
    <color name="status_disconnected">#FFF44336</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="transparent">#00000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="yellow">#FFFFFF00</color>
    <string name="app_description">Remote TV Screen Controller</string>
    <string name="app_name">TV Controller</string>
    <string name="arabic">العربية</string>
    <string name="cancel">إلغاء</string>
    <string name="english">English</string>
    <string name="error_connection">خطأ في الاتصال بالخادم</string>
    <string name="error_invalid_ip">عنوان IP غير صحيح</string>
    <string name="error_invalid_port">رقم المنفذ غير صحيح</string>
    <string name="error_permission">لم يتم منح الصلاحيات المطلوبة</string>
    <string name="go_to_settings">الذهاب للإعدادات</string>
    <string name="hide_overlay">إخفاء الشاشة السوداء</string>
    <string name="language_settings">إعدادات اللغة</string>
    <string name="overlay_countdown">الوقت المتبقي: %d ثانية</string>
    <string name="overlay_expired">انتهى الوقت - Time Expired</string>
    <string name="overlay_message_default">الشاشة مقفلة - Screen Locked</string>
    <string name="permission_battery_message">يرجى إيقاف تحسين البطارية لهذا التطبيق لضمان عمله المستمر</string>
    <string name="permission_battery_title">تحسين البطارية</string>
    <string name="permission_overlay_message">يحتاج التطبيق إلى صلاحية العرض فوق التطبيقات الأخرى للعمل بشكل صحيح</string>
    <string name="permission_overlay_title">صلاحية العرض فوق التطبيقات</string>
    <string name="save">حفظ</string>
    <string name="server_ip">عنوان IP للخادم</string>
    <string name="server_port">منفذ الخادم</string>
    <string name="server_settings">إعدادات الخادم</string>
    <string name="service_description">يتم تشغيل الخدمة في الخلفية للتحكم في الشاشة</string>
    <string name="service_running">خدمة التحكم بالتلفزيون تعمل</string>
    <string name="settings">الإعدادات</string>
    <string name="show_overlay">عرض الشاشة السوداء</string>
    <string name="start_service">تشغيل الخدمة</string>
    <string name="status_connected">متصل</string>
    <string name="status_connecting">جاري الاتصال...</string>
    <string name="status_disconnected">غير متصل</string>
    <string name="stop_service">إيقاف الخدمة</string>
    <string name="test_connection">اختبار الاتصال</string>
    <string name="welcome_subtitle">Welcome to TV Controller App</string>
    <string name="welcome_title">مرحباً بك في تطبيق التحكم بالتلفزيون</string>
    <style name="Theme.Overlay" parent="Theme.Material3.DayNight">
        <item name="android:windowBackground">@color/overlay_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowIsFloating">false</item>
    </style>
    <style name="Theme.TVControllerApp" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/accent_color</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:statusBarColor">@color/primary_dark_color</item>
        
        <item name="android:windowBackground">@color/white</item>
    </style>
    <style name="Theme.TVControllerApp.TV" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="android:windowBackground">@color/primary_dark_color</item>
    </style>
</resources>