!com.tvcontroller.app.MainActivity+com.tvcontroller.app.MainActivity.Companion%com.tvcontroller.app.SettingsActivity,com.tvcontroller.app.TVControllerApplication6com.tvcontroller.app.TVControllerApplication.Companion0com.tvcontroller.app.connection.ServerConnection:com.tvcontroller.app.connection.ServerConnection.Companion*com.tvcontroller.app.receiver.BootReceiver4com.tvcontroller.app.receiver.BootReceiver.Companion-com.tvcontroller.app.receiver.RestartReceiver7com.tvcontroller.app.receiver.RestartReceiver.Companion+com.tvcontroller.app.service.OverlayService5com.tvcontroller.app.service.OverlayService.Companion'com.tvcontroller.app.utils.CrashHandler1com.tvcontroller.app.utils.CrashHandler.Companion!com.tvcontroller.app.utils.Logger(com.tvcontroller.app.utils.NetworkHelper-com.tvcontroller.app.utils.NotificationHelper+com.tvcontroller.app.utils.PermissionHelper<com.tvcontroller.app.utils.PermissionHelper.PermissionStatus-com.tvcontroller.app.utils.PreferencesManager7com.tvcontroller.app.utils.PreferencesManager.Companion(com.tvcontroller.app.utils.ServiceHelper,com.tvcontroller.app.worker.BackgroundWorker6com.tvcontroller.app.worker.BackgroundWorker.Companion0com.tvcontroller.app.worker.ServiceRestartWorker:com.tvcontroller.app.worker.ServiceRestartWorker.Companion4com.tvcontroller.app.databinding.ActivityMainBinding8com.tvcontroller.app.databinding.ActivitySettingsBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           