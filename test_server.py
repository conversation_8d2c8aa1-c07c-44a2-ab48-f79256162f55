#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم اختبار لتطبيق التحكم في شاشات التلفزيون
Test Server for TV Controller App
"""

import socket
import threading
import time
import json
from datetime import datetime

class TVControllerTestServer:
    def __init__(self, host='0.0.0.0', port=8080):
        self.host = host
        self.port = port
        self.clients = []
        self.running = False
        
    def log(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def handle_client(self, client_socket, address):
        self.log(f"عميل جديد متصل من {address}")
        self.clients.append(client_socket)
        
        try:
            while self.running:
                # استقبال رسائل من العميل
                try:
                    data = client_socket.recv(1024)
                    if data:
                        message = data.decode('utf-8').strip()
                        self.log(f"استلام من {address}: {message}")
                        
                        # رد على رسائل العميل
                        if message == "CLIENT_CONNECTED":
                            self.log(f"تأكيد اتصال العميل {address}")
                        elif message == "PING":
                            client_socket.send(b"PONG")
                            
                except socket.timeout:
                    continue
                except:
                    break
                    
        except Exception as e:
            self.log(f"خطأ مع العميل {address}: {e}")
        finally:
            if client_socket in self.clients:
                self.clients.remove(client_socket)
            client_socket.close()
            self.log(f"انقطع الاتصال مع {address}")
    
    def broadcast_command(self, command):
        """إرسال أمر لجميع العملاء المتصلين"""
        disconnected = []
        
        for client in self.clients:
            try:
                client.send(command.encode('utf-8'))
                self.log(f"تم إرسال الأمر: {command}")
            except:
                disconnected.append(client)
        
        # إزالة العملاء المنقطعين
        for client in disconnected:
            if client in self.clients:
                self.clients.remove(client)
    
    def start_server(self):
        """بدء تشغيل الخادم"""
        self.running = True
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            server_socket.bind((self.host, self.port))
            server_socket.listen(5)
            self.log(f"خادم الاختبار يعمل على {self.host}:{self.port}")
            
            while self.running:
                try:
                    client_socket, address = server_socket.accept()
                    client_socket.settimeout(1.0)  # مهلة زمنية للقراءة
                    
                    # إنشاء thread جديد لكل عميل
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    self.log(f"خطأ في قبول الاتصال: {e}")
                    
        except Exception as e:
            self.log(f"خطأ في بدء الخادم: {e}")
        finally:
            server_socket.close()
            self.log("تم إغلاق الخادم")
    
    def stop_server(self):
        """إيقاف الخادم"""
        self.running = False
        for client in self.clients:
            client.close()
        self.clients.clear()

def interactive_mode(server):
    """وضع التفاعل مع الخادم"""
    print("\n=== وضع التحكم التفاعلي ===")
    print("الأوامر المتاحة:")
    print("1. show - عرض الشاشة السوداء")
    print("2. hide - إخفاء الشاشة السوداء") 
    print("3. restart - إعادة تشغيل التطبيق")
    print("4. message:النص - عرض رسالة مخصصة")
    print("5. countdown:30 - عد تنازلي")
    print("6. status - عرض حالة العملاء")
    print("7. quit - إنهاء البرنامج")
    print("=" * 40)
    
    while server.running:
        try:
            command = input("\nأدخل الأمر: ").strip().lower()
            
            if command == "quit" or command == "exit":
                break
            elif command == "show":
                server.broadcast_command("SHOW_BLACK_SCREEN")
            elif command == "hide":
                server.broadcast_command("HIDE_BLACK_SCREEN")
            elif command == "restart":
                server.broadcast_command("RESTART_APP")
            elif command.startswith("message:"):
                message = command[8:]
                server.broadcast_command(f"MESSAGE:{message}")
            elif command.startswith("countdown:"):
                try:
                    seconds = int(command[10:])
                    server.broadcast_command(f"COUNTDOWN:{seconds}")
                except ValueError:
                    print("خطأ: يجب أن يكون العدد صحيحاً")
            elif command == "status":
                print(f"العملاء المتصلون: {len(server.clients)}")
            elif command == "help":
                print("راجع قائمة الأوامر أعلاه")
            else:
                print("أمر غير معروف. اكتب 'help' لعرض المساعدة")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"خطأ: {e}")

def auto_test_mode(server):
    """وضع الاختبار التلقائي"""
    print("\n=== وضع الاختبار التلقائي ===")
    
    test_commands = [
        ("SHOW_BLACK_SCREEN", 5),
        ("MESSAGE:اختبار الرسالة", 3),
        ("COUNTDOWN:10", 12),
        ("HIDE_BLACK_SCREEN", 2),
        ("RESTART_APP", 5)
    ]
    
    for command, wait_time in test_commands:
        if not server.running:
            break
            
        print(f"إرسال: {command}")
        server.broadcast_command(command)
        
        print(f"انتظار {wait_time} ثانية...")
        time.sleep(wait_time)
    
    print("انتهى الاختبار التلقائي")

def main():
    print("🖥️  خادم اختبار تطبيق التحكم في شاشات التلفزيون")
    print("=" * 50)
    
    # إنشاء الخادم
    server = TVControllerTestServer()
    
    # بدء الخادم في thread منفصل
    server_thread = threading.Thread(target=server.start_server)
    server_thread.daemon = True
    server_thread.start()
    
    # انتظار قليل لبدء الخادم
    time.sleep(1)
    
    try:
        # اختيار وضع التشغيل
        print("\nاختر وضع التشغيل:")
        print("1. تفاعلي (i)")
        print("2. اختبار تلقائي (a)")
        
        mode = input("الوضع [i/a]: ").strip().lower()
        
        if mode == 'a' or mode == 'auto':
            auto_test_mode(server)
        else:
            interactive_mode(server)
            
    except KeyboardInterrupt:
        pass
    finally:
        print("\nإيقاف الخادم...")
        server.stop_server()
        print("تم إنهاء البرنامج")

if __name__ == "__main__":
    main()
