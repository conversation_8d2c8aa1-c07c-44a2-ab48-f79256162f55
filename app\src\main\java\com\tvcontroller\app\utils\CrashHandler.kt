package com.tvcontroller.app.utils

import android.content.Context
import android.content.Intent
import android.os.Build
import com.tvcontroller.app.MainActivity
import com.tvcontroller.app.service.OverlayService
import java.io.File
import java.io.FileWriter
import java.io.PrintWriter
import java.io.StringWriter
import java.text.SimpleDateFormat
import java.util.*

class CrashHandler private constructor(private val context: Context) : Thread.UncaughtExceptionHandler {
    
    private val defaultHandler: Thread.UncaughtExceptionHandler? = Thread.getDefaultUncaughtExceptionHandler()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    
    companion object {
        private var instance: CrashHandler? = null
        
        fun init(context: Context) {
            if (instance == null) {
                instance = CrashHandler(context.applicationContext)
                Thread.setDefaultUncaughtExceptionHandler(instance)
                Logger.i("Crash handler initialized")
            }
        }
        
        fun getInstance(): CrashHandler? = instance
    }
    
    override fun uncaughtException(thread: Thread, exception: Throwable) {
        try {
            Logger.e("Uncaught exception in thread ${thread.name}", exception)
            
            // Save crash report
            saveCrashReport(exception, thread)
            
            // Try to restart the service if it was running
            restartServiceIfNeeded()
            
            // Show crash notification
            NotificationHelper.showErrorNotification(
                context,
                "التطبيق توقف بشكل غير متوقع",
                "سيتم إعادة تشغيل التطبيق تلقائياً"
            )
            
            // Restart the app after a delay
            restartApplication()
            
        } catch (e: Exception) {
            Logger.e("Error in crash handler", e)
        } finally {
            // Call the default handler
            defaultHandler?.uncaughtException(thread, exception)
        }
    }
    
    private fun saveCrashReport(exception: Throwable, thread: Thread) {
        try {
            val crashDir = File(context.filesDir, "crashes")
            if (!crashDir.exists()) {
                crashDir.mkdirs()
            }
            
            val timestamp = dateFormat.format(Date())
            val crashFile = File(crashDir, "crash_$timestamp.txt")
            
            FileWriter(crashFile).use { writer ->
                writer.append("=== TV Controller Crash Report ===\n")
                writer.append("Timestamp: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}\n")
                writer.append("Thread: ${thread.name}\n")
                writer.append("Thread ID: ${thread.id}\n")
                writer.append("\n=== Device Information ===\n")
                writer.append("Device: ${Build.DEVICE}\n")
                writer.append("Model: ${Build.MODEL}\n")
                writer.append("Manufacturer: ${Build.MANUFACTURER}\n")
                writer.append("Android Version: ${Build.VERSION.RELEASE}\n")
                writer.append("SDK Version: ${Build.VERSION.SDK_INT}\n")
                writer.append("Build: ${Build.DISPLAY}\n")
                
                writer.append("\n=== Application Information ===\n")
                try {
                    val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
                    writer.append("Package: ${packageInfo.packageName}\n")
                    writer.append("Version: ${packageInfo.versionName} (${packageInfo.versionCode})\n")
                } catch (e: Exception) {
                    writer.append("Package info unavailable: ${e.message}\n")
                }
                
                writer.append("\n=== Memory Information ===\n")
                val runtime = Runtime.getRuntime()
                writer.append("Max Memory: ${runtime.maxMemory() / 1024 / 1024} MB\n")
                writer.append("Total Memory: ${runtime.totalMemory() / 1024 / 1024} MB\n")
                writer.append("Free Memory: ${runtime.freeMemory() / 1024 / 1024} MB\n")
                writer.append("Used Memory: ${(runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024} MB\n")
                
                writer.append("\n=== Exception Details ===\n")
                writer.append("Exception Type: ${exception.javaClass.name}\n")
                writer.append("Message: ${exception.message}\n")
                
                writer.append("\n=== Stack Trace ===\n")
                val stringWriter = StringWriter()
                val printWriter = PrintWriter(stringWriter)
                exception.printStackTrace(printWriter)
                writer.append(stringWriter.toString())
                
                // Add cause if available
                var cause = exception.cause
                var level = 1
                while (cause != null && level <= 5) {
                    writer.append("\n=== Caused by (Level $level) ===\n")
                    writer.append("Exception Type: ${cause.javaClass.name}\n")
                    writer.append("Message: ${cause.message}\n")
                    
                    val causeStringWriter = StringWriter()
                    val causePrintWriter = PrintWriter(causeStringWriter)
                    cause.printStackTrace(causePrintWriter)
                    writer.append(causeStringWriter.toString())
                    
                    cause = cause.cause
                    level++
                }
                
                writer.append("\n=== End of Report ===\n")
            }
            
            Logger.i("Crash report saved: ${crashFile.absolutePath}")
            
            // Clean old crash reports (keep only last 10)
            cleanOldCrashReports(crashDir)
            
        } catch (e: Exception) {
            Logger.e("Failed to save crash report", e)
        }
    }
    
    private fun cleanOldCrashReports(crashDir: File) {
        try {
            val crashFiles = crashDir.listFiles { file ->
                file.name.startsWith("crash_") && file.name.endsWith(".txt")
            }
            
            crashFiles?.sortedByDescending { it.lastModified() }
                ?.drop(10)
                ?.forEach { 
                    it.delete()
                    Logger.d("Deleted old crash report: ${it.name}")
                }
                
        } catch (e: Exception) {
            Logger.e("Failed to clean old crash reports", e)
        }
    }
    
    private fun restartServiceIfNeeded() {
        try {
            // Check if overlay service was running
            if (ServiceHelper.isServiceRunning(context, OverlayService::class.java)) {
                Logger.i("Overlay service was running, will restart it")
                
                // Schedule service restart after app restart
                val restartIntent = Intent(context, OverlayService::class.java)
                restartIntent.putExtra("restart_after_crash", true)
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(restartIntent)
                } else {
                    context.startService(restartIntent)
                }
            }
        } catch (e: Exception) {
            Logger.e("Failed to restart service", e)
        }
    }
    
    private fun restartApplication() {
        try {
            Logger.i("Restarting application after crash...")
            
            // Create restart intent
            val intent = Intent(context, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            intent.putExtra("restarted_after_crash", true)
            
            // Start activity after a short delay
            Thread {
                try {
                    Thread.sleep(2000) // Wait 2 seconds
                    context.startActivity(intent)
                } catch (e: Exception) {
                    Logger.e("Failed to restart application", e)
                }
            }.start()
            
        } catch (e: Exception) {
            Logger.e("Failed to schedule application restart", e)
        }
    }
    
    fun getCrashReports(): List<File> {
        return try {
            val crashDir = File(context.filesDir, "crashes")
            if (!crashDir.exists()) return emptyList()
            
            crashDir.listFiles { file ->
                file.name.startsWith("crash_") && file.name.endsWith(".txt")
            }?.sortedByDescending { it.lastModified() }?.toList() ?: emptyList()
            
        } catch (e: Exception) {
            Logger.e("Failed to get crash reports", e)
            emptyList()
        }
    }
    
    fun getLatestCrashReport(): String? {
        return try {
            val crashReports = getCrashReports()
            if (crashReports.isNotEmpty()) {
                crashReports.first().readText()
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.e("Failed to read latest crash report", e)
            null
        }
    }
    
    fun clearCrashReports() {
        try {
            val crashDir = File(context.filesDir, "crashes")
            if (crashDir.exists()) {
                crashDir.listFiles()?.forEach { it.delete() }
                Logger.i("Crash reports cleared")
            }
        } catch (e: Exception) {
            Logger.e("Failed to clear crash reports", e)
        }
    }
    
    fun getCrashStats(): String {
        return try {
            val crashReports = getCrashReports()
            val totalSize = crashReports.sumOf { it.length() }
            
            buildString {
                append("Crash Statistics:\n")
                append("Total Reports: ${crashReports.size}\n")
                append("Total Size: ${totalSize / 1024} KB\n")
                
                if (crashReports.isNotEmpty()) {
                    val latest = crashReports.first()
                    append("Latest Crash: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date(latest.lastModified()))}\n")
                }
            }
        } catch (e: Exception) {
            "Error getting crash stats: ${e.message}"
        }
    }
}
