<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.tvcontroller.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_main_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="177" endOffset="12"/></Target><Target id="@+id/tv_service_status" view="TextView"><Expressions/><location startLine="56" startOffset="16" endLine="63" endOffset="55"/></Target><Target id="@+id/tv_connection_status" view="TextView"><Expressions/><location startLine="65" startOffset="16" endLine="72" endOffset="55"/></Target><Target id="@+id/tv_server_info" view="TextView"><Expressions/><location startLine="74" startOffset="16" endLine="80" endOffset="54"/></Target><Target id="@+id/btn_start_service" view="Button"><Expressions/><location startLine="93" startOffset="12" endLine="100" endOffset="52"/></Target><Target id="@+id/btn_stop_service" view="Button"><Expressions/><location startLine="102" startOffset="12" endLine="109" endOffset="52"/></Target><Target id="@+id/btn_test_connection" view="Button"><Expressions/><location startLine="111" startOffset="12" endLine="118" endOffset="52"/></Target><Target id="@+id/btn_show_overlay" view="Button"><Expressions/><location startLine="120" startOffset="12" endLine="127" endOffset="52"/></Target><Target id="@+id/btn_settings" view="Button"><Expressions/><location startLine="132" startOffset="8" endLine="139" endOffset="48"/></Target><Target id="@+id/tv_device_info" view="TextView"><Expressions/><location startLine="162" startOffset="16" endLine="169" endOffset="52"/></Target></Targets></Layout>