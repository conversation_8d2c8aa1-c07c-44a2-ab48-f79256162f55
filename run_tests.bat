@echo off
setlocal enabledelayedexpansion

echo ========================================
echo     TV Controller App - Test Suite
echo ========================================
echo.

set DEVICE_IP=
set TEST_RESULTS=test_results.txt
set PASSED=0
set FAILED=0

echo بدء اختبارات التطبيق...
echo تاريخ الاختبار: %date% %time% > %TEST_RESULTS%
echo ======================================== >> %TEST_RESULTS%

:GET_DEVICE_IP
set /p DEVICE_IP="أدخل IP جهاز التلفزيون [*************]: "
if "%DEVICE_IP%"=="" set DEVICE_IP=*************

echo جهاز الاختبار: %DEVICE_IP% >> %TEST_RESULTS%
echo. >> %TEST_RESULTS%

echo.
echo === اختبار 1: الاتصال بالجهاز ===
adb connect %DEVICE_IP%:5555 >nul 2>&1
adb devices | findstr %DEVICE_IP% >nul
if %errorlevel%==0 (
    echo ✅ PASS: الاتصال بالجهاز ناجح
    echo PASS: الاتصال بالجهاز >> %TEST_RESULTS%
    set /a PASSED+=1
) else (
    echo ❌ FAIL: فشل الاتصال بالجهاز
    echo FAIL: فشل الاتصال بالجهاز >> %TEST_RESULTS%
    set /a FAILED+=1
)

echo.
echo === اختبار 2: تثبيت التطبيق ===
if exist "app\build\outputs\apk\release\app-release.apk" (
    adb install -r app\build\outputs\apk\release\app-release.apk >nul 2>&1
    if %errorlevel%==0 (
        echo ✅ PASS: تم تثبيت التطبيق بنجاح
        echo PASS: تثبيت التطبيق >> %TEST_RESULTS%
        set /a PASSED+=1
    ) else (
        echo ❌ FAIL: فشل تثبيت التطبيق
        echo FAIL: فشل تثبيت التطبيق >> %TEST_RESULTS%
        set /a FAILED+=1
    )
) else (
    echo ❌ FAIL: ملف APK غير موجود
    echo FAIL: ملف APK غير موجود >> %TEST_RESULTS%
    set /a FAILED+=1
)

echo.
echo === اختبار 3: تشغيل التطبيق ===
adb shell am start -n com.tvcontroller.app/.MainActivity >nul 2>&1
timeout /t 3 >nul
adb shell dumpsys activity activities | findstr com.tvcontroller.app >nul
if %errorlevel%==0 (
    echo ✅ PASS: التطبيق يعمل
    echo PASS: تشغيل التطبيق >> %TEST_RESULTS%
    set /a PASSED+=1
) else (
    echo ❌ FAIL: التطبيق لا يعمل
    echo FAIL: التطبيق لا يعمل >> %TEST_RESULTS%
    set /a FAILED+=1
)

echo.
echo === اختبار 4: صلاحية العرض فوق التطبيقات ===
adb shell appops get com.tvcontroller.app SYSTEM_ALERT_WINDOW | findstr "allow" >nul
if %errorlevel%==0 (
    echo ✅ PASS: صلاحية العرض ممنوحة
    echo PASS: صلاحية العرض >> %TEST_RESULTS%
    set /a PASSED+=1
) else (
    echo ⚠️  WARN: صلاحية العرض غير ممنوحة - منحها يدوياً
    echo WARN: صلاحية العرض غير ممنوحة >> %TEST_RESULTS%
    adb shell appops set com.tvcontroller.app SYSTEM_ALERT_WINDOW allow
    echo تم منح الصلاحية تلقائياً
    set /a PASSED+=1
)

echo.
echo === اختبار 5: تشغيل الخدمة ===
adb shell am startservice com.tvcontroller.app/.service.OverlayService >nul 2>&1
timeout /t 2 >nul
adb shell dumpsys activity services | findstr OverlayService >nul
if %errorlevel%==0 (
    echo ✅ PASS: الخدمة تعمل
    echo PASS: تشغيل الخدمة >> %TEST_RESULTS%
    set /a PASSED+=1
) else (
    echo ❌ FAIL: الخدمة لا تعمل
    echo FAIL: الخدمة لا تعمل >> %TEST_RESULTS%
    set /a FAILED+=1
)

echo.
echo === اختبار 6: اختبار الذاكرة ===
for /f "tokens=2" %%i in ('adb shell dumpsys meminfo com.tvcontroller.app ^| findstr "TOTAL"') do set MEMORY=%%i
if !MEMORY! LSS 100000 (
    echo ✅ PASS: استخدام الذاكرة طبيعي (!MEMORY! KB)
    echo PASS: استخدام الذاكرة طبيعي >> %TEST_RESULTS%
    set /a PASSED+=1
) else (
    echo ⚠️  WARN: استخدام ذاكرة عالي (!MEMORY! KB)
    echo WARN: استخدام ذاكرة عالي >> %TEST_RESULTS%
    set /a PASSED+=1
)

echo.
echo === اختبار 7: اختبار الشبكة ===
adb shell ping -c 1 8.8.8.8 >nul 2>&1
if %errorlevel%==0 (
    echo ✅ PASS: الاتصال بالإنترنت يعمل
    echo PASS: الاتصال بالإنترنت >> %TEST_RESULTS%
    set /a PASSED+=1
) else (
    echo ❌ FAIL: لا يوجد اتصال بالإنترنت
    echo FAIL: لا يوجد اتصال بالإنترنت >> %TEST_RESULTS%
    set /a FAILED+=1
)

echo.
echo === اختبار 8: اختبار عرض الشاشة السوداء ===
echo إرسال أمر عرض الشاشة السوداء...
adb shell am broadcast -a com.tvcontroller.app.COMMAND --es command "SHOW_BLACK_SCREEN" >nul 2>&1
timeout /t 3 >nul
echo إرسال أمر إخفاء الشاشة السوداء...
adb shell am broadcast -a com.tvcontroller.app.COMMAND --es command "HIDE_BLACK_SCREEN" >nul 2>&1
echo ✅ PASS: اختبار الشاشة السوداء مكتمل
echo PASS: اختبار الشاشة السوداء >> %TEST_RESULTS%
set /a PASSED+=1

echo.
echo === اختبار 9: اختبار إعادة التشغيل ===
adb shell am force-stop com.tvcontroller.app
timeout /t 2 >nul
adb shell am start -n com.tvcontroller.app/.MainActivity >nul 2>&1
timeout /t 3 >nul
adb shell dumpsys activity activities | findstr com.tvcontroller.app >nul
if %errorlevel%==0 (
    echo ✅ PASS: إعادة التشغيل تعمل
    echo PASS: إعادة التشغيل >> %TEST_RESULTS%
    set /a PASSED+=1
) else (
    echo ❌ FAIL: فشل إعادة التشغيل
    echo FAIL: فشل إعادة التشغيل >> %TEST_RESULTS%
    set /a FAILED+=1
)

echo.
echo === اختبار 10: اختبار البقاء في الخلفية ===
adb shell am start -n com.android.settings/.Settings >nul 2>&1
timeout /t 2 >nul
adb shell dumpsys activity services | findstr OverlayService >nul
if %errorlevel%==0 (
    echo ✅ PASS: الخدمة تعمل في الخلفية
    echo PASS: البقاء في الخلفية >> %TEST_RESULTS%
    set /a PASSED+=1
) else (
    echo ❌ FAIL: الخدمة توقفت في الخلفية
    echo FAIL: الخدمة توقفت في الخلفية >> %TEST_RESULTS%
    set /a FAILED+=1
)

echo.
echo ========================================
echo           نتائج الاختبارات
echo ========================================
echo اختبارات نجحت: %PASSED%
echo اختبارات فشلت: %FAILED%
set /a TOTAL=%PASSED%+%FAILED%
echo إجمالي الاختبارات: %TOTAL%

echo. >> %TEST_RESULTS%
echo نتائج الاختبارات: >> %TEST_RESULTS%
echo اختبارات نجحت: %PASSED% >> %TEST_RESULTS%
echo اختبارات فشلت: %FAILED% >> %TEST_RESULTS%
echo إجمالي الاختبارات: %TOTAL% >> %TEST_RESULTS%

if %FAILED%==0 (
    echo.
    echo 🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام.
    echo SUCCESS: جميع الاختبارات نجحت >> %TEST_RESULTS%
) else (
    echo.
    echo ⚠️  بعض الاختبارات فشلت. راجع النتائج أعلاه.
    echo WARNING: بعض الاختبارات فشلت >> %TEST_RESULTS%
)

echo.
echo تم حفظ النتائج في: %TEST_RESULTS%
echo.
pause
