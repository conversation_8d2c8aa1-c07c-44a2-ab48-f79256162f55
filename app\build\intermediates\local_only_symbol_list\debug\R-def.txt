R_DEF: Internal format may change without notice
local
color accent_color
color black
color blue
color green
color orange
color overlay_background
color overlay_progress
color overlay_text
color primary_color
color primary_dark_color
color purple
color purple_200
color purple_500
color purple_700
color red
color semi_transparent
color status_connected
color status_connecting
color status_disconnected
color teal_200
color teal_700
color transparent
color white
color yellow
drawable animated_circle
drawable ic_lock_screen
drawable ic_settings
drawable ic_tv_control
id animated_circle_1
id animated_circle_2
id btn_cancel
id btn_reset_defaults
id btn_save
id btn_settings
id btn_show_overlay
id btn_start_service
id btn_stop_service
id btn_test_connection
id et_reconnect_attempts
id et_server_ip
id et_server_port
id iv_lock_icon
id ll_top_bar
id progress_indicator
id rb_arabic
id rb_english
id switch_auto_start
id toolbar
id tv_connection_status
id tv_countdown
id tv_current_time
id tv_device_info
id tv_overlay_message
id tv_scrolling_text
id tv_server_info
id tv_service_status
id tv_warning
layout activity_main
layout activity_settings
layout overlay_layout
mipmap ic_launcher
mipmap ic_launcher_round
string app_description
string app_name
string arabic
string cancel
string english
string error_connection
string error_invalid_ip
string error_invalid_port
string error_permission
string go_to_settings
string hide_overlay
string language_settings
string overlay_countdown
string overlay_expired
string overlay_message_default
string permission_battery_message
string permission_battery_title
string permission_overlay_message
string permission_overlay_title
string save
string server_ip
string server_port
string server_settings
string service_description
string service_running
string settings
string show_overlay
string start_service
string status_connected
string status_connecting
string status_disconnected
string stop_service
string test_connection
string welcome_subtitle
string welcome_title
style Theme.Overlay
style Theme.TVControllerApp
style Theme.TVControllerApp.TV
xml backup_rules
xml data_extraction_rules
