@echo off
echo ========================================
echo    Emergency APK Builder
echo ========================================
echo.

echo Creating emergency APK package...
echo.

REM Create output directory
if not exist "emergency_apk" mkdir emergency_apk

REM Copy essential files
echo Copying project files...
copy app\src\main\AndroidManifest.xml emergency_apk\
if exist app\src\main\res xcopy app\src\main\res emergency_apk\res\ /E /I /Q
if exist app\src\main\java xcopy app\src\main\java emergency_apk\java\ /E /I /Q

REM Create build info
echo Creating build information...
echo Project: TV Controller > emergency_apk\BUILD_INFO.txt
echo Version: 1.0 >> emergency_apk\BUILD_INFO.txt
echo Package: com.tvcontroller.app >> emergency_apk\BUILD_INFO.txt
echo Build Date: %date% %time% >> emergency_apk\BUILD_INFO.txt
echo Build Type: Emergency Build >> emergency_apk\BUILD_INFO.txt
echo. >> emergency_apk\BUILD_INFO.txt
echo Instructions: >> emergency_apk\BUILD_INFO.txt
echo 1. Import this folder into Android Studio >> emergency_apk\BUILD_INFO.txt
echo 2. Build APK from Android Studio >> emergency_apk\BUILD_INFO.txt
echo 3. Or use online build service >> emergency_apk\BUILD_INFO.txt

REM Create gradle files for Android Studio
echo Creating minimal gradle files...
echo apply plugin: 'com.android.application' > emergency_apk\build.gradle
echo. >> emergency_apk\build.gradle
echo android { >> emergency_apk\build.gradle
echo     compileSdkVersion 34 >> emergency_apk\build.gradle
echo     defaultConfig { >> emergency_apk\build.gradle
echo         applicationId "com.tvcontroller.app" >> emergency_apk\build.gradle
echo         minSdkVersion 21 >> emergency_apk\build.gradle
echo         targetSdkVersion 34 >> emergency_apk\build.gradle
echo         versionCode 1 >> emergency_apk\build.gradle
echo         versionName "1.0" >> emergency_apk\build.gradle
echo     } >> emergency_apk\build.gradle
echo } >> emergency_apk\build.gradle

REM Create project structure info
echo Creating project structure...
echo emergency_apk/ > emergency_apk\STRUCTURE.txt
echo ├── AndroidManifest.xml >> emergency_apk\STRUCTURE.txt
echo ├── build.gradle >> emergency_apk\STRUCTURE.txt
echo ├── res/ >> emergency_apk\STRUCTURE.txt
echo └── java/ >> emergency_apk\STRUCTURE.txt

echo.
echo ========================================
echo    Emergency Package Created!
echo ========================================
echo.
echo Location: emergency_apk\
echo.
echo Next steps:
echo.
echo Option 1 - Android Studio:
echo 1. Download Android Studio
echo 2. Open 'emergency_apk' folder as project
echo 3. Build APK
echo.
echo Option 2 - Online Build:
echo 1. Zip the 'emergency_apk' folder
echo 2. Upload to online Android build service
echo 3. Download compiled APK
echo.
echo Option 3 - Free up space and retry:
echo 1. Delete large files to free 500MB+
echo 2. Run: powershell -ExecutionPolicy Bypass -File build.ps1
echo.

echo Files in emergency package:
dir emergency_apk /b

echo.
pause
