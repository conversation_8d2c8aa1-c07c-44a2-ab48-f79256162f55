package com.tvcontroller.app.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.view.*
import android.widget.TextView
import androidx.core.app.NotificationCompat
import com.tvcontroller.app.MainActivity
import com.tvcontroller.app.R
import com.tvcontroller.app.connection.ServerConnection
import kotlinx.coroutines.*

class OverlayService : Service() {
    
    private lateinit var windowManager: WindowManager
    private var overlayView: View? = null
    private var isOverlayShowing = false
    private lateinit var serverConnection: ServerConnection
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "overlay_service_channel"
    }
    
    override fun onCreate() {
        super.onCreate()
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        serverConnection = ServerConnection(this)
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
        
        // Start server connection
        startServerConnection()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val action = intent?.getStringExtra("action")
        
        when (action) {
            "show_black_screen" -> showBlackScreen()
            "hide_black_screen" -> hideBlackScreen()
            "show_test_overlay" -> showTestOverlay()
            "restart_service" -> restartService()
        }
        
        return START_STICKY // Service will be restarted if killed
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "TV Controller Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Service for TV screen control"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.service_running))
            .setContentText(getString(R.string.service_description))
            .setSmallIcon(R.drawable.ic_tv_control)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    private fun startServerConnection() {
        serviceScope.launch {
            try {
                val sharedPrefs = getSharedPreferences("app_settings", Context.MODE_PRIVATE)
                val serverIp = sharedPrefs.getString("server_ip", "*************") ?: "*************"
                val serverPort = sharedPrefs.getInt("server_port", 8080)
                
                serverConnection.connectToServer(serverIp, serverPort)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    fun showBlackScreen(message: String = getString(R.string.overlay_message_default)) {
        if (isOverlayShowing) return
        
        try {
            overlayView = LayoutInflater.from(this).inflate(R.layout.overlay_layout, null)
            
            val messageText = overlayView?.findViewById<TextView>(R.id.tv_overlay_message)
            messageText?.text = message
            
            val layoutParams = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                            WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    PixelFormat.TRANSLUCENT
                )
            } else {
                WindowManager.LayoutParams(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.TYPE_SYSTEM_OVERLAY,
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                            WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    PixelFormat.TRANSLUCENT
                )
            }
            
            windowManager.addView(overlayView, layoutParams)
            isOverlayShowing = true
            
            // Start scrolling animation
            startScrollingAnimation()
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    fun hideBlackScreen() {
        if (!isOverlayShowing || overlayView == null) return
        
        try {
            windowManager.removeView(overlayView)
            overlayView = null
            isOverlayShowing = false
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    private fun showTestOverlay() {
        showBlackScreen("اختبار الشاشة السوداء - Test Black Screen")
        
        // Auto hide after 5 seconds for testing
        serviceScope.launch {
            delay(5000)
            hideBlackScreen()
        }
    }
    
    private fun startScrollingAnimation() {
        val scrollingText = overlayView?.findViewById<TextView>(R.id.tv_scrolling_text)
        scrollingText?.let { textView ->
            serviceScope.launch {
                while (isOverlayShowing) {
                    textView.animate()
                        .translationX(-textView.width.toFloat())
                        .setDuration(3000)
                        .withEndAction {
                            textView.translationX = textView.width.toFloat()
                            textView.animate()
                                .translationX(0f)
                                .setDuration(0)
                                .start()
                        }
                        .start()
                    delay(3000)
                }
            }
        }
    }
    
    private fun restartService() {
        hideBlackScreen()
        
        serviceScope.launch {
            delay(1000)
            showBlackScreen()
        }
    }
    
    fun updateOverlayMessage(message: String) {
        if (isOverlayShowing && overlayView != null) {
            val messageText = overlayView?.findViewById<TextView>(R.id.tv_overlay_message)
            messageText?.text = message
        }
    }
    
    fun showCountdown(seconds: Int) {
        val countdownMessage = getString(R.string.overlay_countdown, seconds)
        updateOverlayMessage(countdownMessage)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        hideBlackScreen()
        serverConnection.disconnect()
        serviceScope.cancel()
    }
    
    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        // Restart service when task is removed
        val restartIntent = Intent(this, OverlayService::class.java)
        val pendingIntent = PendingIntent.getService(
            this, 1, restartIntent,
            PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
        alarmManager.set(
            AlarmManager.RTC_WAKEUP,
            System.currentTimeMillis() + 1000,
            pendingIntent
        )
    }
}
