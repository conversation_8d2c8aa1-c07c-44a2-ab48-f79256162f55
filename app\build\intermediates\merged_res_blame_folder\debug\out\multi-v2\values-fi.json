{"logs": [{"outputFile": "com.tvcontroller.app-mergeDebugResources-47:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\566c99b1f2c65122017c8029b64a207c\\transformed\\appcompat-1.6.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,13871", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,13947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8944fc038c0e2d5da876e70d3ee0f81b\\transformed\\preference-1.2.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,267,345,480,649,739", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "170,262,340,475,644,734,816"}, "to": {"startLines": "48,50,147,150,153,154,155", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4414,4549,13490,13736,14053,14222,14312", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "4479,4636,13563,13866,14217,14307,14389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b8416fff55a3441d3e464c90290ecef7\\transformed\\leanback-1.0.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,502,630,744,844,935,1066,1193,1301,1425,1570,1699,1824,1933,2027,2153,2244,2358,2469,2581,2684,2804,2913,3040,3163,3270,3381,3497,3613,3724,3836,3923,4005,4103,4237,4382", "endColumns": "106,100,94,93,127,113,99,90,130,126,107,123,144,128,124,108,93,125,90,113,110,111,102,119,108,126,122,106,110,115,115,110,111,86,81,97,133,144,88", "endOffsets": "207,308,403,497,625,739,839,930,1061,1188,1296,1420,1565,1694,1819,1928,2022,2148,2239,2353,2464,2576,2679,2799,2908,3035,3158,3265,3376,3492,3608,3719,3831,3918,4000,4098,4232,4377,4466"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4874,4981,5082,5177,5271,5399,5513,5613,5704,5835,5962,6070,6194,6339,6468,6593,6702,6796,6922,7013,7127,7238,7350,7453,7573,7682,7809,7932,8039,8150,8266,8382,8493,8605,8692,8774,8872,9006,13568", "endColumns": "106,100,94,93,127,113,99,90,130,126,107,123,144,128,124,108,93,125,90,113,110,111,102,119,108,126,122,106,110,115,115,110,111,86,81,97,133,144,88", "endOffsets": "4976,5077,5172,5266,5394,5508,5608,5699,5830,5957,6065,6189,6334,6463,6588,6697,6791,6917,7008,7122,7233,7345,7448,7568,7677,7804,7927,8034,8145,8261,8377,8488,8600,8687,8769,8867,9001,9146,13652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ba6274a1892fdd5def28bdd9767110ee\\transformed\\core-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3392,3488,3590,3688,3793,3898,4010,13952", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3483,3585,3683,3788,3893,4005,4121,14048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4dde4ede0bc8a81ca43b3c1837b4aed4\\transformed\\material-1.11.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1025,1118,1193,1258,1346,1411,1477,1535,1606,1672,1726,1836,1896,1960,2014,2087,2203,2287,2368,2501,2586,2671,2804,2894,2968,3020,3071,3137,3214,3296,3380,3454,3528,3607,3684,3756,3863,3952,4028,4119,4214,4288,4361,4455,4509,4583,4655,4741,4827,4889,4953,5016,5087,5188,5291,5386,5486,5542,5597", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "263,339,413,496,585,667,763,871,955,1020,1113,1188,1253,1341,1406,1472,1530,1601,1667,1721,1831,1891,1955,2009,2082,2198,2282,2363,2496,2581,2666,2799,2889,2963,3015,3066,3132,3209,3291,3375,3449,3523,3602,3679,3751,3858,3947,4023,4114,4209,4283,4356,4450,4504,4578,4650,4736,4822,4884,4948,5011,5082,5183,5286,5381,5481,5537,5592,5671"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2988,3064,3138,3221,3310,4126,4222,4330,4484,4641,4734,4809,9151,9239,9304,9370,9428,9499,9565,9619,9729,9789,9853,9907,9980,10096,10180,10261,10394,10479,10564,10697,10787,10861,10913,10964,11030,11107,11189,11273,11347,11421,11500,11577,11649,11756,11845,11921,12012,12107,12181,12254,12348,12402,12476,12548,12634,12720,12782,12846,12909,12980,13081,13184,13279,13379,13435,13657", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "313,3059,3133,3216,3305,3387,4217,4325,4409,4544,4729,4804,4869,9234,9299,9365,9423,9494,9560,9614,9724,9784,9848,9902,9975,10091,10175,10256,10389,10474,10559,10692,10782,10856,10908,10959,11025,11102,11184,11268,11342,11416,11495,11572,11644,11751,11840,11916,12007,12102,12176,12249,12343,12397,12471,12543,12629,12715,12777,12841,12904,12975,13076,13179,13274,13374,13430,13485,13731"}}]}]}