// Generated by view binder compiler. Do not edit!
package com.tvcontroller.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tvcontroller.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnSettings;

  @NonNull
  public final Button btnShowOverlay;

  @NonNull
  public final Button btnStartService;

  @NonNull
  public final Button btnStopService;

  @NonNull
  public final Button btnTestConnection;

  @NonNull
  public final TextView tvConnectionStatus;

  @NonNull
  public final TextView tvDeviceInfo;

  @NonNull
  public final TextView tvServerInfo;

  @NonNull
  public final TextView tvServiceStatus;

  private ActivityMainBinding(@NonNull ScrollView rootView, @NonNull Button btnSettings,
      @NonNull Button btnShowOverlay, @NonNull Button btnStartService,
      @NonNull Button btnStopService, @NonNull Button btnTestConnection,
      @NonNull TextView tvConnectionStatus, @NonNull TextView tvDeviceInfo,
      @NonNull TextView tvServerInfo, @NonNull TextView tvServiceStatus) {
    this.rootView = rootView;
    this.btnSettings = btnSettings;
    this.btnShowOverlay = btnShowOverlay;
    this.btnStartService = btnStartService;
    this.btnStopService = btnStopService;
    this.btnTestConnection = btnTestConnection;
    this.tvConnectionStatus = tvConnectionStatus;
    this.tvDeviceInfo = tvDeviceInfo;
    this.tvServerInfo = tvServerInfo;
    this.tvServiceStatus = tvServiceStatus;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_settings;
      Button btnSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnSettings == null) {
        break missingId;
      }

      id = R.id.btn_show_overlay;
      Button btnShowOverlay = ViewBindings.findChildViewById(rootView, id);
      if (btnShowOverlay == null) {
        break missingId;
      }

      id = R.id.btn_start_service;
      Button btnStartService = ViewBindings.findChildViewById(rootView, id);
      if (btnStartService == null) {
        break missingId;
      }

      id = R.id.btn_stop_service;
      Button btnStopService = ViewBindings.findChildViewById(rootView, id);
      if (btnStopService == null) {
        break missingId;
      }

      id = R.id.btn_test_connection;
      Button btnTestConnection = ViewBindings.findChildViewById(rootView, id);
      if (btnTestConnection == null) {
        break missingId;
      }

      id = R.id.tv_connection_status;
      TextView tvConnectionStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvConnectionStatus == null) {
        break missingId;
      }

      id = R.id.tv_device_info;
      TextView tvDeviceInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceInfo == null) {
        break missingId;
      }

      id = R.id.tv_server_info;
      TextView tvServerInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvServerInfo == null) {
        break missingId;
      }

      id = R.id.tv_service_status;
      TextView tvServiceStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvServiceStatus == null) {
        break missingId;
      }

      return new ActivityMainBinding((ScrollView) rootView, btnSettings, btnShowOverlay,
          btnStartService, btnStopService, btnTestConnection, tvConnectionStatus, tvDeviceInfo,
          tvServerInfo, tvServiceStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
