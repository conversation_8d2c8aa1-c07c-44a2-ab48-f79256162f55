@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TV Controller App - Setup & Deploy
echo ========================================
echo.
echo هذا السكريبت سيقوم بـ:
echo 1. بناء التطبيق
echo 2. إنشاء keystore للتوقيع
echo 3. تثبيت التطبيق على الجهاز
echo 4. إجراء اختبارات شاملة
echo 5. تشغيل خادم الاختبار
echo.

set DEVICE_IP=
set /p DEVICE_IP="أدخل IP جهاز التلفزيون [*************]: "
if "%DEVICE_IP%"=="" set DEVICE_IP=*************

echo.
echo === الخطوة 1: فحص المتطلبات ===

echo فحص Java...
java -version >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Java متوفر
) else (
    echo ❌ Java غير متوفر. يرجى تثبيت Java JDK
    pause
    exit /b 1
)

echo فحص Android SDK...
if exist "%ANDROID_HOME%\platform-tools\adb.exe" (
    echo ✅ Android SDK متوفر
) else (
    echo ❌ Android SDK غير متوفر. يرجى تثبيت Android Studio
    pause
    exit /b 1
)

echo.
echo === الخطوة 2: إنشاء Keystore ===
if not exist "app\release-key.keystore" (
    echo إنشاء keystore للتوقيع...
    keytool -genkey -v -keystore app\release-key.keystore -alias tvcontroller -keyalg RSA -keysize 2048 -validity 10000 -storepass tvcontroller123 -keypass tvcontroller123 -dname "CN=TV Controller, OU=Development, O=TV Controller App, L=City, S=State, C=US"
    if %errorlevel%==0 (
        echo ✅ تم إنشاء keystore بنجاح
    ) else (
        echo ❌ فشل إنشاء keystore
        pause
        exit /b 1
    )
) else (
    echo ✅ keystore موجود مسبقاً
)

echo.
echo === الخطوة 3: بناء التطبيق ===
echo تنظيف المشروع...
call gradlew clean
if %errorlevel% NEQ 0 (
    echo ❌ فشل تنظيف المشروع
    pause
    exit /b 1
)

echo بناء APK للإنتاج...
call gradlew assembleRelease
if %errorlevel% NEQ 0 (
    echo ❌ فشل بناء APK
    pause
    exit /b 1
)

echo ✅ تم بناء APK بنجاح

echo.
echo === الخطوة 4: الاتصال بالجهاز ===
echo الاتصال بـ %DEVICE_IP%...
adb connect %DEVICE_IP%:5555
timeout /t 3 >nul

adb devices | findstr %DEVICE_IP% >nul
if %errorlevel%==0 (
    echo ✅ متصل بالجهاز
) else (
    echo ❌ فشل الاتصال بالجهاز
    echo تأكد من:
    echo - تمكين خيارات المطور
    echo - تمكين تصحيح USB
    echo - الاتصال بنفس الشبكة
    pause
    exit /b 1
)

echo.
echo === الخطوة 5: تثبيت التطبيق ===
echo تثبيت التطبيق على الجهاز...
adb install -r app\build\outputs\apk\release\app-release.apk
if %errorlevel%==0 (
    echo ✅ تم تثبيت التطبيق بنجاح
) else (
    echo ❌ فشل تثبيت التطبيق
    echo جرب تمكين "مصادر غير معروفة" على الجهاز
    pause
    exit /b 1
)

echo.
echo === الخطوة 6: إعداد الصلاحيات ===
echo منح صلاحية العرض فوق التطبيقات...
adb shell appops set com.tvcontroller.app SYSTEM_ALERT_WINDOW allow
echo ✅ تم منح الصلاحيات

echo إيقاف تحسين البطارية...
adb shell dumpsys deviceidle whitelist +com.tvcontroller.app
echo ✅ تم إيقاف تحسين البطارية

echo.
echo === الخطوة 7: تشغيل التطبيق ===
echo تشغيل التطبيق...
adb shell am start -n com.tvcontroller.app/.MainActivity
timeout /t 3 >nul

adb shell dumpsys activity activities | findstr com.tvcontroller.app >nul
if %errorlevel%==0 (
    echo ✅ التطبيق يعمل
) else (
    echo ❌ فشل تشغيل التطبيق
    pause
    exit /b 1
)

echo.
echo === الخطوة 8: تشغيل الخدمة ===
echo تشغيل خدمة الخلفية...
adb shell am startservice com.tvcontroller.app/.service.OverlayService
timeout /t 2 >nul

adb shell dumpsys activity services | findstr OverlayService >nul
if %errorlevel%==0 (
    echo ✅ الخدمة تعمل
) else (
    echo ⚠️  الخدمة قد تحتاج تشغيل يدوي من التطبيق
)

echo.
echo === الخطوة 9: اختبار سريع ===
echo اختبار عرض الشاشة السوداء...
adb shell am broadcast -a com.tvcontroller.app.COMMAND --es command "SHOW_BLACK_SCREEN"
echo انتظار 3 ثوان...
timeout /t 3 >nul
adb shell am broadcast -a com.tvcontroller.app.COMMAND --es command "HIDE_BLACK_SCREEN"
echo ✅ اختبار الشاشة السوداء مكتمل

echo.
echo ========================================
echo           التثبيت مكتمل!
echo ========================================
echo.
echo التطبيق جاهز للاستخدام على %DEVICE_IP%
echo.
echo الخطوات التالية:
echo 1. افتح التطبيق على التلفزيون
echo 2. اذهب للإعدادات وأدخل IP الخادم
echo 3. اضغط "تشغيل الخدمة"
echo 4. شغّل خادم الاختبار باستخدام test_server.py
echo.

set /p run_server="هل تريد تشغيل خادم الاختبار الآن؟ [y/n]: "
if /i "%run_server%"=="y" (
    echo.
    echo تشغيل خادم الاختبار...
    python test_server.py
) else (
    echo.
    echo يمكنك تشغيل خادم الاختبار لاحقاً باستخدام:
    echo python test_server.py
)

echo.
echo ملفات مفيدة:
echo - README.md: دليل شامل
echo - QUICK_START.md: دليل البدء السريع
echo - test_server.py: خادم اختبار
echo - performance_monitor.bat: مراقب الأداء
echo - run_tests.bat: اختبارات شاملة
echo.

pause
