@echo off
echo ========================================
echo    Manual APK Build Process
echo ========================================
echo.

echo Step 1: Check available space...
for /f "tokens=3" %%a in ('dir c:\ ^| findstr "bytes free"') do set FREE_SPACE=%%a
echo Free space: %FREE_SPACE% bytes
echo.

echo Step 2: Find Java keytool...
set KEYTOOL_PATH=
if exist "C:\Program Files\Java\jdk-24.0.1\bin\keytool.exe" (
    set KEYTOOL_PATH=C:\Program Files\Java\jdk-24.0.1\bin\keytool.exe
) else if exist "C:\Program Files\Java\jre-24.0.1\bin\keytool.exe" (
    set KEYTOOL_PATH=C:\Program Files\Java\jre-24.0.1\bin\keytool.exe
) else (
    echo Searching for keytool...
    for /r "C:\Program Files\Java" %%f in (keytool.exe) do (
        set KEYTOOL_PATH=%%f
        goto found_keytool
    )
)

:found_keytool
if defined KEYTOOL_PATH (
    echo ✅ Found keytool: %KEYTOOL_PATH%
) else (
    echo ❌ keytool not found!
    goto manual_keystore
)

echo.
echo Step 3: Create keystore...
if not exist "app\release-key.keystore" (
    echo Creating keystore with keytool...
    "%KEYTOOL_PATH%" -genkey -v -keystore app\release-key.keystore -alias tvcontroller -keyalg RSA -keysize 2048 -validity 10000 -storepass tvcontroller123 -keypass tvcontroller123 -dname "CN=TV Controller, OU=Development, O=TV Controller App, L=City, S=State, C=US"
    
    if exist "app\release-key.keystore" (
        echo ✅ Keystore created successfully!
    ) else (
        goto manual_keystore
    )
) else (
    echo ✅ Keystore already exists!
)
goto check_gradle

:manual_keystore
echo Creating dummy keystore file...
echo dummy_keystore_content > app\release-key.keystore
echo ⚠️ Created dummy keystore (APK won't be properly signed)

:check_gradle
echo.
echo Step 4: Check Gradle...
if exist "gradle\wrapper\gradle-wrapper.jar" (
    echo ✅ Gradle wrapper exists
    goto try_gradle
) else (
    echo ❌ Gradle wrapper missing
    goto alternative_build
)

:try_gradle
echo.
echo Step 5: Try building with Gradle...
echo This might fail due to disk space...
call gradlew assembleRelease

if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✅ APK built successfully!
    goto success
) else (
    echo ❌ Gradle build failed
    goto alternative_build
)

:alternative_build
echo.
echo ========================================
echo    Alternative Build Methods
echo ========================================
echo.
echo Since Gradle failed, here are alternatives:
echo.
echo 1. Use Android Studio (Recommended):
echo    - Download: https://developer.android.com/studio
echo    - Open this project folder
echo    - Build > Build APK
echo.
echo 2. Free up disk space:
echo    - Run: cleanup_space.bat
echo    - Need at least 500MB free
echo    - Then try: fix_gradle.bat
echo.
echo 3. Move project to another drive:
echo    - Copy to D:\ or external drive
echo    - More space available there
echo.
echo 4. Use online build service:
echo    - Upload to GitHub
echo    - Use GitHub Actions
echo    - Or use online Android build tools
echo.
echo 5. Manual compilation (Advanced):
echo    - Requires Android SDK
echo    - Complex process
echo.
goto end

:success
echo.
echo ========================================
echo         BUILD SUCCESSFUL!
echo ========================================
echo.
echo APK Location:
echo app\build\outputs\apk\release\app-release.apk
echo.
echo File size:
for %%A in (app\build\outputs\apk\release\app-release.apk) do echo %%~zA bytes
echo.
echo To install on Android TV:
echo 1. adb connect [TV_IP]:5555
echo 2. adb install app\build\outputs\apk\release\app-release.apk
echo.

:end
echo.
echo Current project files:
dir /b *.bat *.ps1 2>nul
echo.
pause
