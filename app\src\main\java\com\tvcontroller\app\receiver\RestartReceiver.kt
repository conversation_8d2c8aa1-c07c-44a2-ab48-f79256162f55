package com.tvcontroller.app.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.tvcontroller.app.service.OverlayService

class RestartReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "RestartReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "RestartReceiver triggered")
        
        try {
            // Restart the overlay service
            val serviceIntent = Intent(context, OverlayService::class.java)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
            
            Log.d(TAG, "Service restarted successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restart service: ${e.message}")
        }
    }
}
