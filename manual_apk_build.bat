@echo off
echo ========================================
echo    Manual APK Build Process
echo ========================================
echo.

echo This script will guide you through building the APK manually
echo.

echo Step 1: Check if Android SDK is available
echo Looking for Android SDK...

REM Check common Android SDK locations
set ANDROID_HOME=
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
    echo Found Android SDK at: %ANDROID_HOME%
) else if exist "C:\Android\Sdk" (
    set ANDROID_HOME=C:\Android\Sdk
    echo Found Android SDK at: %ANDROID_HOME%
) else (
    echo Android SDK not found in common locations
    echo.
    echo Please install Android Studio or Android SDK
    echo Download from: https://developer.android.com/studio
    echo.
    pause
    exit /b 1
)

echo.
echo Step 2: Set up build environment
set AAPT=%ANDROID_HOME%\build-tools\34.0.0\aapt.exe
set DX=%ANDROID_HOME%\build-tools\34.0.0\dx.bat
set ZIPALIGN=%ANDROID_HOME%\build-tools\34.0.0\zipalign.exe
set APKSIGNER=%ANDROID_HOME%\build-tools\34.0.0\apksigner.bat

echo.
echo Step 3: Create build directories
if not exist "manual_build" mkdir manual_build
if not exist "manual_build\classes" mkdir manual_build\classes
if not exist "manual_build\dex" mkdir manual_build\dex
if not exist "manual_build\apk" mkdir manual_build\apk

echo.
echo Step 4: Compile resources
echo Compiling Android resources...
%AAPT% package -f -m -J manual_build\gen -S app\src\main\res -M app\src\main\AndroidManifest.xml -I %ANDROID_HOME%\platforms\android-34\android.jar

echo.
echo Step 5: Compile Java/Kotlin source
echo Note: This requires javac and kotlinc to be available
echo.
echo For a complete build, use Android Studio:
echo 1. Open this project in Android Studio
echo 2. Build > Build Bundle(s) / APK(s) > Build APK(s)
echo.
echo Or fix Gradle and run: gradlew assembleDebug
echo.

echo Current project files:
dir app\src\main\java\com\tvcontroller\app /b

echo.
echo To continue with Android Studio:
echo 1. Install Android Studio from: https://developer.android.com/studio
echo 2. Open this folder as a project
echo 3. Let Android Studio sync and download dependencies
echo 4. Build > Build Bundle(s) / APK(s) > Build APK(s)
echo.

pause
