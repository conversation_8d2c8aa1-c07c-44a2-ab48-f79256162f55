package com.tvcontroller.app.connection

import android.content.Context
import android.content.Intent
import android.util.Log
import com.tvcontroller.app.service.OverlayService
import kotlinx.coroutines.*
import okhttp3.*
import java.io.IOException
import java.net.Socket
import java.util.concurrent.TimeUnit

class ServerConnection(private val context: Context) {
    
    private var webSocket: WebSocket? = null
    private var tcpSocket: Socket? = null
    private var isConnected = false
    private var reconnectAttempts = 0
    private val maxReconnectAttempts = 10
    private val connectionScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    companion object {
        private const val TAG = "ServerConnection"
        private const val RECONNECT_DELAY = 5000L // 5 seconds
    }
    
    // WebSocket connection
    fun connectToServerWebSocket(ip: String, port: Int) {
        connectionScope.launch {
            try {
                val client = OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(0, TimeUnit.SECONDS)
                    .writeTimeout(10, TimeUnit.SECONDS)
                    .build()
                
                val request = Request.Builder()
                    .url("ws://$ip:$port")
                    .build()
                
                webSocket = client.newWebSocket(request, object : WebSocketListener() {
                    override fun onOpen(webSocket: WebSocket, response: Response) {
                        Log.d(TAG, "WebSocket connected")
                        isConnected = true
                        reconnectAttempts = 0
                        
                        // Send initial connection message
                        webSocket.send("CLIENT_CONNECTED")
                    }
                    
                    override fun onMessage(webSocket: WebSocket, text: String) {
                        Log.d(TAG, "Received message: $text")
                        handleServerCommand(text)
                    }
                    
                    override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                        Log.d(TAG, "WebSocket closing: $reason")
                        isConnected = false
                    }
                    
                    override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                        Log.d(TAG, "WebSocket closed: $reason")
                        isConnected = false
                        attemptReconnect(ip, port)
                    }
                    
                    override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                        Log.e(TAG, "WebSocket error: ${t.message}")
                        isConnected = false
                        attemptReconnect(ip, port)
                    }
                })
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to connect WebSocket: ${e.message}")
                attemptReconnect(ip, port)
            }
        }
    }
    
    // TCP Socket connection (fallback)
    fun connectToServerTCP(ip: String, port: Int) {
        connectionScope.launch {
            try {
                tcpSocket = Socket(ip, port)
                isConnected = true
                reconnectAttempts = 0
                
                Log.d(TAG, "TCP connected to $ip:$port")
                
                // Listen for messages
                val inputStream = tcpSocket?.getInputStream()
                val buffer = ByteArray(1024)
                
                while (isConnected && tcpSocket?.isConnected == true) {
                    try {
                        val bytes = inputStream?.read(buffer) ?: -1
                        if (bytes > 0) {
                            val command = String(buffer, 0, bytes).trim()
                            Log.d(TAG, "Received TCP command: $command")
                            handleServerCommand(command)
                        } else if (bytes == -1) {
                            break // Connection closed
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error reading TCP data: ${e.message}")
                        break
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to connect TCP: ${e.message}")
                isConnected = false
                attemptReconnect(ip, port)
            }
        }
    }
    
    private fun handleServerCommand(command: String) {
        Log.d(TAG, "Processing command: $command")
        
        when (command.uppercase()) {
            "SHOW_BLACK_SCREEN" -> {
                val intent = Intent(context, OverlayService::class.java)
                intent.putExtra("action", "show_black_screen")
                context.startService(intent)
            }
            
            "HIDE_BLACK_SCREEN" -> {
                val intent = Intent(context, OverlayService::class.java)
                intent.putExtra("action", "hide_black_screen")
                context.startService(intent)
            }
            
            "RESTART_APP" -> {
                val intent = Intent(context, OverlayService::class.java)
                intent.putExtra("action", "restart_service")
                context.startService(intent)
            }
            
            else -> {
                // Handle custom messages
                if (command.startsWith("MESSAGE:")) {
                    val message = command.substring(8)
                    showCustomMessage(message)
                } else if (command.startsWith("COUNTDOWN:")) {
                    val seconds = command.substring(10).toIntOrNull() ?: 0
                    showCountdown(seconds)
                }
            }
        }
    }
    
    private fun showCustomMessage(message: String) {
        val intent = Intent(context, OverlayService::class.java)
        intent.putExtra("action", "show_black_screen")
        intent.putExtra("message", message)
        context.startService(intent)
    }
    
    private fun showCountdown(seconds: Int) {
        connectionScope.launch {
            for (i in seconds downTo 1) {
                val intent = Intent(context, OverlayService::class.java)
                intent.putExtra("action", "show_countdown")
                intent.putExtra("seconds", i)
                context.startService(intent)
                
                delay(1000)
            }
            
            // Show final message
            val intent = Intent(context, OverlayService::class.java)
            intent.putExtra("action", "show_black_screen")
            intent.putExtra("message", "انتهى الوقت - Time Expired")
            context.startService(intent)
        }
    }
    
    private fun attemptReconnect(ip: String, port: Int) {
        if (reconnectAttempts >= maxReconnectAttempts) {
            Log.e(TAG, "Max reconnection attempts reached")
            return
        }
        
        reconnectAttempts++
        Log.d(TAG, "Attempting reconnection #$reconnectAttempts")
        
        connectionScope.launch {
            delay(RECONNECT_DELAY)
            
            // Try WebSocket first, then TCP
            try {
                connectToServerWebSocket(ip, port)
            } catch (e: Exception) {
                Log.d(TAG, "WebSocket reconnect failed, trying TCP")
                connectToServerTCP(ip, port)
            }
        }
    }
    
    fun sendMessage(message: String): Boolean {
        return try {
            when {
                webSocket != null && isConnected -> {
                    webSocket?.send(message)
                    true
                }
                tcpSocket != null && tcpSocket?.isConnected == true -> {
                    tcpSocket?.getOutputStream()?.write(message.toByteArray())
                    tcpSocket?.getOutputStream()?.flush()
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send message: ${e.message}")
            false
        }
    }
    
    fun isConnected(): Boolean = isConnected
    
    fun disconnect() {
        isConnected = false
        connectionScope.cancel()
        
        try {
            webSocket?.close(1000, "Client disconnecting")
            tcpSocket?.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error during disconnect: ${e.message}")
        }
        
        webSocket = null
        tcpSocket = null
    }
    
    // Auto-connect with both protocols
    fun connectToServer(ip: String, port: Int) {
        Log.d(TAG, "Connecting to server $ip:$port")
        
        // Try WebSocket first
        connectToServerWebSocket(ip, port)
        
        // If WebSocket fails after 10 seconds, try TCP
        connectionScope.launch {
            delay(10000)
            if (!isConnected) {
                Log.d(TAG, "WebSocket failed, trying TCP")
                connectToServerTCP(ip, port)
            }
        }
    }
}
