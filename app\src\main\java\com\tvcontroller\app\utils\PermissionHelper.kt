package com.tvcontroller.app.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import androidx.annotation.RequiresApi

object PermissionHelper {
    
    /**
     * Check if the app has overlay permission
     */
    fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true // Permission not required for older versions
        }
    }
    
    /**
     * Check if battery optimization is disabled for the app
     */
    fun isBatteryOptimizationDisabled(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            powerManager.isIgnoringBatteryOptimizations(context.packageName)
        } else {
            true // Not applicable for older versions
        }
    }
    
    /**
     * Request overlay permission
     */
    @RequiresApi(Build.VERSION_CODES.M)
    fun requestOverlayPermission(context: Context) {
        val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)
        intent.data = Uri.parse("package:${context.packageName}")
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        context.startActivity(intent)
    }
    
    /**
     * Request to disable battery optimization
     */
    @RequiresApi(Build.VERSION_CODES.M)
    fun requestDisableBatteryOptimization(context: Context) {
        val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
        intent.data = Uri.parse("package:${context.packageName}")
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        context.startActivity(intent)
    }
    
    /**
     * Open app settings
     */
    fun openAppSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        intent.data = Uri.parse("package:${context.packageName}")
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        context.startActivity(intent)
    }
    
    /**
     * Check if device is Android TV
     */
    fun isAndroidTV(context: Context): Boolean {
        return context.packageManager.hasSystemFeature("android.software.leanback")
    }
    
    /**
     * Check if device supports touch screen
     */
    fun hasTouchScreen(context: Context): Boolean {
        return context.packageManager.hasSystemFeature("android.hardware.touchscreen")
    }
    
    /**
     * Get device information for debugging
     */
    fun getDeviceInfo(): String {
        return buildString {
            append("Device: ${Build.DEVICE}\n")
            append("Model: ${Build.MODEL}\n")
            append("Manufacturer: ${Build.MANUFACTURER}\n")
            append("Android Version: ${Build.VERSION.RELEASE}\n")
            append("SDK Version: ${Build.VERSION.SDK_INT}\n")
            append("Build: ${Build.DISPLAY}\n")
        }
    }
    
    /**
     * Check if app can be installed without developer options
     * This is a workaround for some TV devices
     */
    fun canInstallWithoutDeveloperOptions(context: Context): Boolean {
        return try {
            // Check if ADB is enabled
            val adbEnabled = Settings.Global.getInt(
                context.contentResolver,
                Settings.Global.ADB_ENABLED, 0
            ) == 1
            
            // Check if unknown sources is enabled
            val unknownSources = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.packageManager.canRequestPackageInstalls()
            } else {
                @Suppress("DEPRECATION")
                Settings.Secure.getInt(
                    context.contentResolver,
                    Settings.Secure.INSTALL_NON_MARKET_APPS, 0
                ) == 1
            }
            
            adbEnabled || unknownSources
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Enable unknown sources programmatically (requires system permissions)
     */
    fun enableUnknownSources(context: Context): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // For Android 8.0+, need to request install permission
                if (!context.packageManager.canRequestPackageInstalls()) {
                    val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES)
                    intent.data = Uri.parse("package:${context.packageName}")
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    context.startActivity(intent)
                }
                context.packageManager.canRequestPackageInstalls()
            } else {
                // For older versions, try to enable unknown sources
                @Suppress("DEPRECATION")
                Settings.Secure.putInt(
                    context.contentResolver,
                    Settings.Secure.INSTALL_NON_MARKET_APPS, 1
                )
                true
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Check all required permissions
     */
    fun checkAllPermissions(context: Context): PermissionStatus {
        return PermissionStatus(
            hasOverlayPermission = hasOverlayPermission(context),
            isBatteryOptimizationDisabled = isBatteryOptimizationDisabled(context),
            canInstallApps = canInstallWithoutDeveloperOptions(context),
            isAndroidTV = isAndroidTV(context)
        )
    }
    
    data class PermissionStatus(
        val hasOverlayPermission: Boolean,
        val isBatteryOptimizationDisabled: Boolean,
        val canInstallApps: Boolean,
        val isAndroidTV: Boolean
    ) {
        fun allGranted(): Boolean {
            return hasOverlayPermission && isBatteryOptimizationDisabled
        }
        
        fun getStatusText(): String {
            return buildString {
                append("Overlay Permission: ${if (hasOverlayPermission) "✓" else "✗"}\n")
                append("Battery Optimization: ${if (isBatteryOptimizationDisabled) "✓" else "✗"}\n")
                append("Install Apps: ${if (canInstallApps) "✓" else "✗"}\n")
                append("Android TV: ${if (isAndroidTV) "✓" else "✗"}")
            }
        }
    }
}
