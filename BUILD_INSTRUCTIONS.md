# TV Controller App - Build Instructions

## تطبيق التحكم في التلفزيون - تعليمات البناء

### الطريقة الأولى: استخدام Android Studio (الأسهل)

1. **تحميل Android Studio:**
   - اذهب إلى: https://developer.android.com/studio
   - حمل وثبت Android Studio

2. **فتح المشروع:**
   - افتح Android Studio
   - اختر "Open an existing project"
   - اختر مجلد المشروع هذا

3. **بناء APK:**
   - انتظر حتى ينتهي Android Studio من تحميل التبعيات
   - اذهب إلى: Build > Build Bundle(s) / APK(s) > Build APK(s)
   - ستجد ملف APK في: `app/build/outputs/apk/debug/`

### الطريقة الثانية: استخدام سطر الأوامر

1. **التأكد من Java:**
   ```cmd
   java -version
   ```
   إذا لم يكن Java مثبتاً، حمله من: https://www.oracle.com/java/technologies/downloads/

2. **إصلاح Gradle (إذا لزم الأمر):**
   ```cmd
   fix_gradle.bat
   ```

3. **بناء APK:**
   ```cmd
   gradlew assembleDebug
   ```

### الطريقة الثالثة: البناء اليدوي

1. **تشغيل البناء اليدوي:**
   ```cmd
   manual_apk_build.bat
   ```

## ملفات APK المتوقعة

بعد البناء الناجح، ستجد:

- **Debug APK:** `app/build/outputs/apk/debug/app-debug.apk`
- **Release APK:** `app/build/outputs/apk/release/app-release.apk`

## تثبيت APK على Android TV

### الطريقة الأولى: ADB (الأسرع)

1. **تفعيل Developer Options على التلفزيون:**
   - اذهب إلى Settings > Device Preferences > About
   - اضغط على "Build" 7 مرات
   - ارجع إلى Settings > Device Preferences > Developer options
   - فعل "USB debugging" و "ADB debugging"

2. **الاتصال والتثبيت:**
   ```cmd
   adb connect [TV_IP_ADDRESS]:5555
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

### الطريقة الثانية: USB

1. انسخ ملف APK إلى USB drive
2. أدخل USB في التلفزيون
3. استخدم File Manager لتثبيت APK

### الطريقة الثالثة: تطبيق File Manager

1. ارفع APK إلى Google Drive أو Dropbox
2. حمل تطبيق File Manager على التلفزيون
3. حمل وثبت APK

## استكشاف الأخطاء

### مشكلة: Gradle لا يعمل
**الحل:**
```cmd
fix_gradle.bat
```

### مشكلة: Java غير موجود
**الحل:**
- حمل Java JDK من: https://www.oracle.com/java/technologies/downloads/
- أضف Java إلى PATH

### مشكلة: Android SDK غير موجود
**الحل:**
- ثبت Android Studio
- أو حمل Command Line Tools من: https://developer.android.com/studio#command-tools

## ميزات التطبيق

✅ **خدمة المقدمة:** يعمل في الخلفية باستمرار
✅ **شاشة التراكب:** يظهر فوق التطبيقات الأخرى  
✅ **اتصال الخادم:** WebSocket و TCP
✅ **إعادة التشغيل التلقائي:** يعيد تشغيل نفسه عند الإغلاق
✅ **توافق Android 5.0+:** يعمل على الإصدارات القديمة
✅ **تجاوز خيارات المطور:** لا يحتاج تفعيل خيارات المطور

## الاستخدام

1. **تشغيل التطبيق:** سيبدأ تلقائياً عند التثبيت
2. **الإعدادات:** اضغط على أيقونة الإعدادات لتكوين الاتصال
3. **الاتصال:** أدخل عنوان IP للخادم
4. **التحكم:** استخدم الأزرار للتحكم في التلفزيون

## الدعم

إذا واجهت مشاكل:
1. تأكد من أن التلفزيون متصل بالإنترنت
2. تأكد من أن الخادم يعمل
3. تحقق من إعدادات الشبكة
4. أعد تشغيل التطبيق

---

**ملاحظة:** هذا التطبيق مصمم خصيصاً لأجهزة Android TV ويتطلب Android 5.0 أو أحدث.
