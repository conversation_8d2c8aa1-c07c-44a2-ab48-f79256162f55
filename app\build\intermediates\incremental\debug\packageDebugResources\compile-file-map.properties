#Sat Jul 05 08:25:31 AST 2025
com.tvcontroller.app-packageDebugResources-2\:/layout/overlay_layout.xml=C\:\\shasha\\app\\build\\intermediates\\packaged_res\\debug\\layout\\overlay_layout.xml
com.tvcontroller.app-main-5\:/drawable/ic_tv_control.xml=C\:\\shasha\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_tv_control.xml
com.tvcontroller.app-main-5\:/drawable/animated_circle.xml=C\:\\shasha\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\animated_circle.xml
com.tvcontroller.app-main-5\:/drawable/ic_settings.xml=C\:\\shasha\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_settings.xml
com.tvcontroller.app-main-5\:/xml/backup_rules.xml=C\:\\shasha\\app\\build\\intermediates\\packaged_res\\debug\\xml\\backup_rules.xml
com.tvcontroller.app-main-5\:/drawable/ic_lock_screen.xml=C\:\\shasha\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_lock_screen.xml
com.tvcontroller.app-packageDebugResources-2\:/layout/activity_settings.xml=C\:\\shasha\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_settings.xml
com.tvcontroller.app-packageDebugResources-2\:/layout/activity_main.xml=C\:\\shasha\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_main.xml
com.tvcontroller.app-main-5\:/mipmap-hdpi/ic_launcher.png=C\:\\shasha\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher.png
com.tvcontroller.app-main-5\:/xml/data_extraction_rules.xml=C\:\\shasha\\app\\build\\intermediates\\packaged_res\\debug\\xml\\data_extraction_rules.xml
