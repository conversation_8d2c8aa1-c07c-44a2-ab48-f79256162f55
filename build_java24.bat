@echo off
echo ========================================
echo   Java 24 Compatible Build
echo ========================================
echo.

echo Setting Java compatibility flags...
set JAVA_OPTS=--enable-native-access=ALL-UNNAMED
set GRADLE_OPTS=--enable-native-access=ALL-UNNAMED

echo.
echo Cleaning Gradle cache...
if exist "%USERPROFILE%\.gradle\caches" (
    rmdir /s /q "%USERPROFILE%\.gradle\caches"
    echo Gradle cache cleared
)

echo.
echo Building with Java 24 compatibility...
call gradlew clean assembleRelease --enable-native-access=ALL-UNNAMED --warning-mode=none

if exist "app\build\outputs\apk\release\app-release.apk" (
    echo.
    echo ========================================
    echo         BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo APK Location: app\build\outputs\apk\release\app-release.apk
    
    for %%A in (app\build\outputs\apk\release\app-release.apk) do echo APK Size: %%~zA bytes
    echo.
) else (
    echo.
    echo ========================================
    echo         BUILD FAILED!
    echo ========================================
    echo.
    echo Try these alternatives:
    echo 1. Download Java 17: https://adoptium.net/temurin/releases/?version=17
    echo 2. Use Android Studio
    echo 3. Use online build service
    echo.
)

pause
