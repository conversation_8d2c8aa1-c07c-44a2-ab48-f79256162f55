package com.tvcontroller.app

import android.content.SharedPreferences
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.preference.PreferenceManager
import com.tvcontroller.app.databinding.ActivitySettingsBinding
import com.tvcontroller.app.utils.PermissionHelper

class SettingsActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivitySettingsBinding
    private lateinit var sharedPreferences: SharedPreferences
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        sharedPreferences = PreferenceManager.getDefaultSharedPreferences(this)
        
        setupUI()
        loadSettings()
    }
    
    private fun setupUI() {
        // Set up toolbar
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.settings)
        
        // Load current settings
        loadSettings()
        
        // Save button
        binding.btnSave.setOnClickListener {
            saveSettings()
        }
        
        // Cancel button
        binding.btnCancel.setOnClickListener {
            finish()
        }
        
        // Test connection button
        binding.btnTestConnection.setOnClickListener {
            testConnection()
        }
        
        // Reset to defaults button
        binding.btnResetDefaults.setOnClickListener {
            resetToDefaults()
        }
        
        // Show device info
        updateDeviceInfo()
    }
    
    private fun loadSettings() {
        val serverIp = sharedPreferences.getString("server_ip", "*************") ?: "*************"
        val serverPort = sharedPreferences.getInt("server_port", 8080)
        val language = sharedPreferences.getString("language", "ar") ?: "ar"
        val autoStart = sharedPreferences.getBoolean("auto_start", true)
        val reconnectAttempts = sharedPreferences.getInt("reconnect_attempts", 10)
        
        binding.etServerIp.setText(serverIp)
        binding.etServerPort.setText(serverPort.toString())
        binding.switchAutoStart.isChecked = autoStart
        binding.etReconnectAttempts.setText(reconnectAttempts.toString())
        
        // Set language radio buttons
        when (language) {
            "ar" -> binding.rbArabic.isChecked = true
            "en" -> binding.rbEnglish.isChecked = true
        }
    }
    
    private fun saveSettings() {
        try {
            val serverIp = binding.etServerIp.text.toString().trim()
            val serverPortText = binding.etServerPort.text.toString().trim()
            val reconnectAttemptsText = binding.etReconnectAttempts.text.toString().trim()
            
            // Validate inputs
            if (serverIp.isEmpty()) {
                binding.etServerIp.error = getString(R.string.error_invalid_ip)
                return
            }
            
            if (serverPortText.isEmpty()) {
                binding.etServerPort.error = getString(R.string.error_invalid_port)
                return
            }
            
            val serverPort = serverPortText.toIntOrNull()
            if (serverPort == null || serverPort < 1 || serverPort > 65535) {
                binding.etServerPort.error = getString(R.string.error_invalid_port)
                return
            }
            
            val reconnectAttempts = reconnectAttemptsText.toIntOrNull() ?: 10
            if (reconnectAttempts < 1 || reconnectAttempts > 100) {
                binding.etReconnectAttempts.error = "يجب أن يكون بين 1 و 100"
                return
            }
            
            // Determine selected language
            val language = when {
                binding.rbArabic.isChecked -> "ar"
                binding.rbEnglish.isChecked -> "en"
                else -> "ar"
            }
            
            // Save to preferences
            with(sharedPreferences.edit()) {
                putString("server_ip", serverIp)
                putInt("server_port", serverPort)
                putString("language", language)
                putBoolean("auto_start", binding.switchAutoStart.isChecked)
                putInt("reconnect_attempts", reconnectAttempts)
                apply()
            }
            
            Toast.makeText(this, "تم حفظ الإعدادات", Toast.LENGTH_SHORT).show()
            finish()
            
        } catch (e: Exception) {
            Toast.makeText(this, "خطأ في حفظ الإعدادات: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    private fun testConnection() {
        val serverIp = binding.etServerIp.text.toString().trim()
        val serverPortText = binding.etServerPort.text.toString().trim()
        
        if (serverIp.isEmpty() || serverPortText.isEmpty()) {
            Toast.makeText(this, "يرجى إدخال عنوان IP والمنفذ", Toast.LENGTH_SHORT).show()
            return
        }
        
        val serverPort = serverPortText.toIntOrNull()
        if (serverPort == null || serverPort < 1 || serverPort > 65535) {
            Toast.makeText(this, "رقم المنفذ غير صحيح", Toast.LENGTH_SHORT).show()
            return
        }
        
        binding.btnTestConnection.isEnabled = false
        binding.btnTestConnection.text = "جاري الاختبار..."
        
        Thread {
            try {
                val socket = java.net.Socket()
                socket.connect(java.net.InetSocketAddress(serverIp, serverPort), 5000)
                socket.close()
                
                runOnUiThread {
                    binding.btnTestConnection.isEnabled = true
                    binding.btnTestConnection.text = getString(R.string.test_connection)
                    Toast.makeText(this, "الاتصال ناجح! ✓", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                runOnUiThread {
                    binding.btnTestConnection.isEnabled = true
                    binding.btnTestConnection.text = getString(R.string.test_connection)
                    Toast.makeText(this, "فشل الاتصال: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }.start()
    }
    
    private fun resetToDefaults() {
        binding.etServerIp.setText("*************")
        binding.etServerPort.setText("8080")
        binding.rbArabic.isChecked = true
        binding.switchAutoStart.isChecked = true
        binding.etReconnectAttempts.setText("10")
        
        Toast.makeText(this, "تم إعادة تعيين الإعدادات الافتراضية", Toast.LENGTH_SHORT).show()
    }
    
    private fun updateDeviceInfo() {
        val deviceInfo = PermissionHelper.getDeviceInfo()
        val permissionStatus = PermissionHelper.checkAllPermissions(this)
        
        val info = buildString {
            append("معلومات الجهاز:\n")
            append(deviceInfo)
            append("\nحالة الصلاحيات:\n")
            append(permissionStatus.getStatusText())
        }
        
        binding.tvDeviceInfo.text = info
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
