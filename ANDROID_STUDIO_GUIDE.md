# دليل بناء التطبيق باستخدام Android Studio

## 🚀 الطريقة الموصى بها - Android Studio

### الخطوة 1: تحميل وتثبيت Android Studio

1. **تحميل Android Studio:**
   - اذه<PERSON> إلى: https://developer.android.com/studio
   - حمل النسخة الأحدث لنظام Windows
   - حجم التحميل: حوالي 1 جيجابايت

2. **التثبيت:**
   - شغل ملف التثبيت كمدير (Run as Administrator)
   - اتبع التعليمات الافتراضية
   - اختر "Standard" installation
   - انتظر تحميل SDK components

### الخطوة 2: فتح المشروع

1. **تشغيل Android Studio:**
   - افتح Android Studio
   - اختر "Open an existing project"

2. **اختيار المجلد:**
   - اختر مجلد المشروع هذا (المجلد الذي يحتوي على build.gradle)
   - اضغط OK

3. **انتظار التحميل:**
   - سيقوم Android Studio بتحميل التبعيات تلقائياً
   - قد يستغرق 5-10 دقائق في المرة الأولى
   - ستظهر رسالة "Gradle sync finished" عند الانتهاء

### الخطوة 3: بناء APK

1. **من القائمة العلوية:**
   ```
   Build > Build Bundle(s) / APK(s) > Build APK(s)
   ```

2. **انتظار البناء:**
   - سيظهر شريط تقدم في الأسفل
   - انتظر حتى ظهور "BUILD SUCCESSFUL"

3. **العثور على APK:**
   - سيظهر إشعار في الأسفل مع رابط "locate"
   - أو اذهب إلى: `app/build/outputs/apk/debug/`
   - ستجد ملف: `app-debug.apk`

### الخطوة 4: تثبيت APK على Android TV

#### الطريقة الأولى: ADB (الأسرع)

1. **تفعيل Developer Options على التلفزيون:**
   ```
   Settings > Device Preferences > About
   اضغط على "Build" 7 مرات
   ارجع إلى Settings > Device Preferences > Developer options
   فعل "USB debugging" و "ADB debugging"
   ```

2. **الاتصال والتثبيت:**
   ```cmd
   adb connect [عنوان_IP_للتلفزيون]:5555
   adb install app-debug.apk
   ```

#### الطريقة الثانية: USB Drive

1. انسخ `app-debug.apk` إلى USB drive
2. أدخل USB في التلفزيون
3. استخدم File Manager لفتح وتثبيت APK

#### الطريقة الثالثة: تطبيق File Manager

1. ارفع APK إلى Google Drive أو Dropbox
2. حمل تطبيق File Manager على التلفزيون
3. حمل وثبت APK من التطبيق

## 🔧 حل المشاكل الشائعة

### مشكلة: Gradle sync failed

**الحل:**
1. تأكد من اتصال الإنترنت
2. File > Invalidate Caches and Restart
3. أعد فتح المشروع

### مشكلة: SDK not found

**الحل:**
1. File > Project Structure
2. SDK Location
3. تأكد من مسار Android SDK

### مشكلة: Build failed

**الحل:**
1. Build > Clean Project
2. Build > Rebuild Project
3. تحقق من رسائل الخطأ في Build tab

### مشكلة: APK لا يعمل على التلفزيون

**الحل:**
1. تأكد من أن التلفزيون يدعم Android 5.0+
2. فعل "Unknown sources" في إعدادات التلفزيون
3. أعد تشغيل التلفزيون بعد التثبيت

## 📱 اختبار التطبيق

### بعد التثبيت:

1. **تشغيل التطبيق:**
   - ستجد التطبيق في قائمة التطبيقات
   - اسم التطبيق: "TV Controller"

2. **الإعدادات الأولى:**
   - اضغط على "الإعدادات"
   - أدخل عنوان IP للخادم
   - احفظ الإعدادات

3. **اختبار الاتصال:**
   - اضغط "اختبار الاتصال"
   - يجب أن تظهر رسالة نجاح

4. **تشغيل الخدمة:**
   - اضغط "تشغيل الخدمة"
   - سيظهر إشعار دائم
   - التطبيق سيعمل في الخلفية

## 🎯 الميزات المتاحة

✅ **خدمة الخلفية:** تعمل باستمرار
✅ **شاشة التراكب:** تظهر فوق التطبيقات
✅ **اتصال الخادم:** WebSocket و TCP
✅ **إعادة التشغيل:** تلقائي عند الإغلاق
✅ **توافق واسع:** Android 5.0+
✅ **سهولة الاستخدام:** واجهة بسيطة

## 📞 الدعم

إذا واجهت مشاكل:

1. **تحقق من المتطلبات:**
   - Android Studio مثبت بشكل صحيح
   - اتصال إنترنت مستقر
   - مساحة كافية على القرص (5+ جيجابايت)

2. **أعد المحاولة:**
   - أغلق وأعد فتح Android Studio
   - Clean وأعد Build المشروع
   - أعد تشغيل الكمبيوتر إذا لزم الأمر

3. **تحقق من الإعدادات:**
   - تأكد من إعدادات الشبكة
   - تحقق من عنوان IP للخادم
   - تأكد من تشغيل الخادم

---

**نصيحة:** احتفظ بنسخة من APK في مكان آمن لاستخدامها على أجهزة أخرى!
