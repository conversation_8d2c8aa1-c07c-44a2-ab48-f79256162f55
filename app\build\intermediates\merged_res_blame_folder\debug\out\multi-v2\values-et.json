{"logs": [{"outputFile": "com.tvcontroller.app-mergeDebugResources-47:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8944fc038c0e2d5da876e70d3ee0f81b\\transformed\\preference-1.2.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,180,267,343,481,650,733", "endColumns": "74,86,75,137,168,82,77", "endOffsets": "175,262,338,476,645,728,806"}, "to": {"startLines": "48,50,147,150,153,154,155", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4488,4627,13594,13842,14164,14333,14416", "endColumns": "74,86,75,137,168,82,77", "endOffsets": "4558,4709,13665,13975,14328,14411,14489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\566c99b1f2c65122017c8029b64a207c\\transformed\\appcompat-1.6.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,422,521,632,718,820,937,1018,1095,1187,1281,1377,1479,1588,1682,1783,1877,1969,2062,2145,2256,2360,2459,2569,2671,2770,2936,13980", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "417,516,627,713,815,932,1013,1090,1182,1276,1372,1474,1583,1677,1778,1872,1964,2057,2140,2251,2355,2454,2564,2666,2765,2931,3033,14058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b8416fff55a3441d3e464c90290ecef7\\transformed\\leanback-1.0.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,502,623,737,837,930,1052,1172,1275,1394,1550,1679,1806,1916,2007,2132,2222,2324,2432,2533,2631,2746,2859,2993,3125,3243,3364,3478,3596,3706,3820,3907,3991,4094,4241,4407", "endColumns": "106,100,94,93,120,113,99,92,121,119,102,118,155,128,126,109,90,124,89,101,107,100,97,114,112,133,131,117,120,113,117,109,113,86,83,102,146,165,90", "endOffsets": "207,308,403,497,618,732,832,925,1047,1167,1270,1389,1545,1674,1801,1911,2002,2127,2217,2319,2427,2528,2626,2741,2854,2988,3120,3238,3359,3473,3591,3701,3815,3902,3986,4089,4236,4402,4493"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4931,5038,5139,5234,5328,5449,5563,5663,5756,5878,5998,6101,6220,6376,6505,6632,6742,6833,6958,7048,7150,7258,7359,7457,7572,7685,7819,7951,8069,8190,8304,8422,8532,8646,8733,8817,8920,9067,13670", "endColumns": "106,100,94,93,120,113,99,92,121,119,102,118,155,128,126,109,90,124,89,101,107,100,97,114,112,133,131,117,120,113,117,109,113,86,83,102,146,165,90", "endOffsets": "5033,5134,5229,5323,5444,5558,5658,5751,5873,5993,6096,6215,6371,6500,6627,6737,6828,6953,7043,7145,7253,7354,7452,7567,7680,7814,7946,8064,8185,8299,8417,8527,8641,8728,8812,8915,9062,9228,13756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ba6274a1892fdd5def28bdd9767110ee\\transformed\\core-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "38,39,40,41,42,43,44,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3461,3556,3658,3756,3859,3965,4070,14063", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3551,3653,3751,3854,3960,4065,4185,14159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4dde4ede0bc8a81ca43b3c1837b4aed4\\transformed\\material-1.11.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1051,1136,1204,1268,1355,1419,1483,1542,1614,1678,1732,1851,1911,1972,2026,2099,2232,2316,2409,2547,2627,2706,2832,2920,2999,3054,3105,3171,3244,3323,3409,3488,3561,3636,3710,3782,3895,3983,4060,4151,4243,4315,4389,4480,4534,4616,4685,4768,4854,4916,4980,5043,5111,5214,5317,5414,5515,5574,5629", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,78,84,91,86,98,116,81,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,92,137,79,78,125,87,78,54,50,65,72,78,85,78,72,74,73,71,112,87,76,90,91,71,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80", "endOffsets": "261,341,420,505,597,684,783,900,982,1046,1131,1199,1263,1350,1414,1478,1537,1609,1673,1727,1846,1906,1967,2021,2094,2227,2311,2404,2542,2622,2701,2827,2915,2994,3049,3100,3166,3239,3318,3404,3483,3556,3631,3705,3777,3890,3978,4055,4146,4238,4310,4384,4475,4529,4611,4680,4763,4849,4911,4975,5038,5106,5209,5312,5409,5510,5569,5624,5705"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3038,3118,3197,3282,3374,4190,4289,4406,4563,4714,4799,4867,9233,9320,9384,9448,9507,9579,9643,9697,9816,9876,9937,9991,10064,10197,10281,10374,10512,10592,10671,10797,10885,10964,11019,11070,11136,11209,11288,11374,11453,11526,11601,11675,11747,11860,11948,12025,12116,12208,12280,12354,12445,12499,12581,12650,12733,12819,12881,12945,13008,13076,13179,13282,13379,13480,13539,13761", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,149", "endColumns": "12,79,78,84,91,86,98,116,81,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,92,137,79,78,125,87,78,54,50,65,72,78,85,78,72,74,73,71,112,87,76,90,91,71,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80", "endOffsets": "311,3113,3192,3277,3369,3456,4284,4401,4483,4622,4794,4862,4926,9315,9379,9443,9502,9574,9638,9692,9811,9871,9932,9986,10059,10192,10276,10369,10507,10587,10666,10792,10880,10959,11014,11065,11131,11204,11283,11369,11448,11521,11596,11670,11742,11855,11943,12020,12111,12203,12275,12349,12440,12494,12576,12645,12728,12814,12876,12940,13003,13071,13174,13277,13374,13475,13534,13589,13837"}}]}]}