{"logs": [{"outputFile": "com.tvcontroller.app-mergeDebugResources-47:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4dde4ede0bc8a81ca43b3c1837b4aed4\\transformed\\material-1.11.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1179,1270,1336,1399,1487,1549,1616,1674,1745,1804,1858,1972,2032,2095,2149,2222,2341,2427,2510,2649,2734,2821,2954,3042,3120,3177,3228,3294,3366,3442,3532,3615,3688,3765,3846,3920,4029,4119,4198,4289,4385,4459,4540,4635,4689,4771,4837,4924,5010,5072,5136,5199,5272,5379,5489,5587,5693,5754,5809", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,132,87,77,56,50,65,71,75,89,82,72,76,80,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1174,1265,1331,1394,1482,1544,1611,1669,1740,1799,1853,1967,2027,2090,2144,2217,2336,2422,2505,2644,2729,2816,2949,3037,3115,3172,3223,3289,3361,3437,3527,3610,3683,3760,3841,3915,4024,4114,4193,4284,4380,4454,4535,4630,4684,4766,4832,4919,5005,5067,5131,5194,5267,5374,5484,5582,5688,5749,5804,5886"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,51,53,54,55,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3226,3304,3392,3500,4318,4414,4530,4687,4842,4933,4999,9442,9530,9592,9659,9717,9788,9847,9901,10015,10075,10138,10192,10265,10384,10470,10553,10692,10777,10864,10997,11085,11163,11220,11271,11337,11409,11485,11575,11658,11731,11808,11889,11963,12072,12162,12241,12332,12428,12502,12583,12678,12732,12814,12880,12967,13053,13115,13179,13242,13315,13422,13532,13630,13736,13797,14024", "endLines": "7,35,36,37,38,39,47,48,49,51,53,54,55,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,151", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,132,87,77,56,50,65,71,75,89,82,72,76,80,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81", "endOffsets": "419,3221,3299,3387,3495,3586,4409,4525,4608,4749,4928,4994,5057,9525,9587,9654,9712,9783,9842,9896,10010,10070,10133,10187,10260,10379,10465,10548,10687,10772,10859,10992,11080,11158,11215,11266,11332,11404,11480,11570,11653,11726,11803,11884,11958,12067,12157,12236,12327,12423,12497,12578,12673,12727,12809,12875,12962,13048,13110,13174,13237,13310,13417,13527,13625,13731,13792,13847,14101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\566c99b1f2c65122017c8029b64a207c\\transformed\\appcompat-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "424,533,635,743,829,934,1052,1133,1212,1303,1396,1491,1585,1685,1778,1873,1968,2059,2150,2249,2355,2461,2559,2666,2773,2878,3048,14248", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "528,630,738,824,929,1047,1128,1207,1298,1391,1486,1580,1680,1773,1868,1963,2054,2145,2244,2350,2456,2554,2661,2768,2873,3043,3143,14325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ba6274a1892fdd5def28bdd9767110ee\\transformed\\core-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,154", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3591,3691,3793,3894,3995,4100,4205,14330", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3686,3788,3889,3990,4095,4200,4313,14426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b8416fff55a3441d3e464c90290ecef7\\transformed\\leanback-1.0.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,507,628,739,835,926,1049,1172,1282,1410,1571,1694,1817,1923,2020,2158,2253,2357,2463,2576,2679,2801,2920,3038,3156,3274,3397,3525,3657,3780,3907,3994,4077,4189,4325,4485", "endColumns": "106,100,99,93,120,110,95,90,122,122,109,127,160,122,122,105,96,137,94,103,105,112,102,121,118,117,117,117,122,127,131,122,126,86,82,111,135,159,90", "endOffsets": "207,308,408,502,623,734,830,921,1044,1167,1277,1405,1566,1689,1812,1918,2015,2153,2248,2352,2458,2571,2674,2796,2915,3033,3151,3269,3392,3520,3652,3775,3902,3989,4072,4184,4320,4480,4571"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5062,5169,5270,5370,5464,5585,5696,5792,5883,6006,6129,6239,6367,6528,6651,6774,6880,6977,7115,7210,7314,7420,7533,7636,7758,7877,7995,8113,8231,8354,8482,8614,8737,8864,8951,9034,9146,9282,13933", "endColumns": "106,100,99,93,120,110,95,90,122,122,109,127,160,122,122,105,96,137,94,103,105,112,102,121,118,117,117,117,122,127,131,122,126,86,82,111,135,159,90", "endOffsets": "5164,5265,5365,5459,5580,5691,5787,5878,6001,6124,6234,6362,6523,6646,6769,6875,6972,7110,7205,7309,7415,7528,7631,7753,7872,7990,8108,8226,8349,8477,8609,8732,8859,8946,9029,9141,9277,9437,14019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8944fc038c0e2d5da876e70d3ee0f81b\\transformed\\preference-1.2.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,267,348,490,659,744", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "174,262,343,485,654,739,822"}, "to": {"startLines": "50,52,149,152,155,156,157", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4613,4754,13852,14106,14431,14600,14685", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "4682,4837,13928,14243,14595,14680,14763"}}]}]}