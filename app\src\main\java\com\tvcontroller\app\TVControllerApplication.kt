package com.tvcontroller.app

import android.app.Application
import android.content.Context
import android.util.Log
import com.tvcontroller.app.utils.CrashHandler
import com.tvcontroller.app.utils.Logger
import com.tvcontroller.app.utils.NotificationHelper
import com.tvcontroller.app.utils.PreferencesManager
import com.tvcontroller.app.worker.BackgroundWorker

class TVControllerApplication : Application() {
    
    companion object {
        private var instance: TVControllerApplication? = null
        
        fun getInstance(): TVControllerApplication {
            return instance ?: throw IllegalStateException("Application not initialized")
        }
        
        fun getContext(): Context {
            return getInstance().applicationContext
        }
    }
    
    lateinit var preferencesManager: PreferencesManager
        private set
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        
        // Initialize crash handler first
        CrashHandler.init(this)

        // Initialize logging
        Logger.init(this, enableFileLogging = true, level = Log.DEBUG)
        Logger.i("TV Controller Application starting...")

        // Initialize preferences
        preferencesManager = PreferencesManager(this)
        Logger.i("Preferences manager initialized")
        
        // Create notification channels
        NotificationHelper.createNotificationChannels(this)
        Logger.i("Notification channels created")
        
        // Schedule background monitoring if auto-start is enabled
        if (preferencesManager.autoStart) {
            BackgroundWorker.schedulePeriodicWork(this)
            Logger.i("Background monitoring scheduled")
        }
        
        // Log application info
        logApplicationInfo()
        
        Logger.i("TV Controller Application initialized successfully")
    }
    
    override fun onTerminate() {
        super.onTerminate()
        Logger.i("TV Controller Application terminating...")
        
        // Cancel background work
        BackgroundWorker.cancelPeriodicWork(this)
        
        Logger.i("TV Controller Application terminated")
    }
    
    override fun onLowMemory() {
        super.onLowMemory()
        Logger.w("Application received low memory warning")
        
        // Clear any non-essential caches or data
        System.gc()
    }
    
    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        
        when (level) {
            TRIM_MEMORY_RUNNING_MODERATE -> {
                Logger.w("Memory trim: Running moderate")
            }
            TRIM_MEMORY_RUNNING_LOW -> {
                Logger.w("Memory trim: Running low")
            }
            TRIM_MEMORY_RUNNING_CRITICAL -> {
                Logger.w("Memory trim: Running critical")
                // Release non-essential resources
                System.gc()
            }
            TRIM_MEMORY_UI_HIDDEN -> {
                Logger.d("Memory trim: UI hidden")
            }
            TRIM_MEMORY_BACKGROUND -> {
                Logger.d("Memory trim: Background")
            }
            TRIM_MEMORY_MODERATE -> {
                Logger.w("Memory trim: Moderate")
            }
            TRIM_MEMORY_COMPLETE -> {
                Logger.w("Memory trim: Complete")
                // Release all non-critical resources
                System.gc()
            }
        }
    }
    
    private fun logApplicationInfo() {
        try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            
            Logger.i("=== Application Information ===")
            Logger.i("Package Name: ${packageInfo.packageName}")
            Logger.i("Version Name: ${packageInfo.versionName}")
            Logger.i("Version Code: ${packageInfo.versionCode}")
            Logger.i("Target SDK: ${applicationInfo.targetSdkVersion}")
            Logger.i("Min SDK: ${packageInfo.applicationInfo?.let { 
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                    it.minSdkVersion
                } else {
                    "Unknown"
                }
            }}")
            Logger.i("Data Directory: ${applicationInfo.dataDir}")
            Logger.i("Source Directory: ${applicationInfo.sourceDir}")
            Logger.i("Process Name: ${applicationInfo.processName}")
            Logger.i("UID: ${applicationInfo.uid}")
            Logger.i("Enabled: ${applicationInfo.enabled}")
            Logger.i("Debuggable: ${(applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0}")
            
            // Device information
            Logger.i("=== Device Information ===")
            Logger.i("Device: ${android.os.Build.DEVICE}")
            Logger.i("Model: ${android.os.Build.MODEL}")
            Logger.i("Manufacturer: ${android.os.Build.MANUFACTURER}")
            Logger.i("Brand: ${android.os.Build.BRAND}")
            Logger.i("Product: ${android.os.Build.PRODUCT}")
            Logger.i("Android Version: ${android.os.Build.VERSION.RELEASE}")
            Logger.i("SDK Version: ${android.os.Build.VERSION.SDK_INT}")
            Logger.i("Build ID: ${android.os.Build.ID}")
            Logger.i("Build Type: ${android.os.Build.TYPE}")
            Logger.i("Build Tags: ${android.os.Build.TAGS}")
            
            // Runtime information
            Logger.i("=== Runtime Information ===")
            val runtime = Runtime.getRuntime()
            Logger.i("Max Memory: ${runtime.maxMemory() / 1024 / 1024} MB")
            Logger.i("Total Memory: ${runtime.totalMemory() / 1024 / 1024} MB")
            Logger.i("Free Memory: ${runtime.freeMemory() / 1024 / 1024} MB")
            Logger.i("Available Processors: ${runtime.availableProcessors()}")
            
            // Settings summary
            Logger.i("=== Current Settings ===")
            Logger.i(preferencesManager.getSettingsSummary())
            
        } catch (e: Exception) {
            Logger.e("Failed to log application info", e)
        }
    }
    
    fun restartApplication() {
        Logger.i("Restarting application...")
        
        try {
            val intent = packageManager.getLaunchIntentForPackage(packageName)
            intent?.let {
                it.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP)
                it.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(it)
            }
            
            // Exit current process
            android.os.Process.killProcess(android.os.Process.myPid())
            
        } catch (e: Exception) {
            Logger.e("Failed to restart application", e)
        }
    }
    
    fun getApplicationStats(): String {
        return try {
            val runtime = Runtime.getRuntime()
            val usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024
            val maxMemory = runtime.maxMemory() / 1024 / 1024
            val memoryUsage = (usedMemory * 100) / maxMemory
            
            buildString {
                append("=== Application Statistics ===\n")
                append("Memory Usage: $usedMemory MB / $maxMemory MB ($memoryUsage%)\n")
                append("Uptime: ${getUptimeString()}\n")
                append("Total Connections: ${preferencesManager.totalConnections}\n")
                append("Commands Received: ${preferencesManager.totalCommandsReceived}\n")
                append("Service Enabled: ${preferencesManager.serviceEnabled}\n")
                append("Auto Start: ${preferencesManager.autoStart}\n")
                append("Log Files: ${Logger.getLogFiles().size}\n")
            }
        } catch (e: Exception) {
            "Error getting application stats: ${e.message}"
        }
    }
    
    private fun getUptimeString(): String {
        val uptimeMillis = android.os.SystemClock.elapsedRealtime()
        val seconds = (uptimeMillis / 1000) % 60
        val minutes = (uptimeMillis / (1000 * 60)) % 60
        val hours = (uptimeMillis / (1000 * 60 * 60)) % 24
        val days = uptimeMillis / (1000 * 60 * 60 * 24)
        
        return when {
            days > 0 -> "${days}d ${hours}h ${minutes}m"
            hours > 0 -> "${hours}h ${minutes}m"
            minutes > 0 -> "${minutes}m ${seconds}s"
            else -> "${seconds}s"
        }
    }
}
