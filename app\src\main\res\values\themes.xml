<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.TVControllerApp" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/accent_color</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/primary_dark_color</item>
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/white</item>
    </style>

    <!-- Fullscreen theme for overlay -->
    <style name="Theme.Overlay" parent="Theme.Material3.DayNight">
        <item name="android:windowBackground">@color/overlay_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <!-- TV Theme -->
    <style name="Theme.TVControllerApp.TV" parent="Theme.Leanback">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorAccent">@color/accent_color</item>
    </style>
</resources>
