{"logs": [{"outputFile": "com.tvcontroller.app-mergeDebugResources-45:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4dde4ede0bc8a81ca43b3c1837b4aed4\\transformed\\material-1.11.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1110,1207,1290,1356,1458,1523,1598,1654,1733,1793,1847,1969,2028,2090,2144,2226,2361,2453,2537,2681,2760,2841,2982,3075,3154,3209,3260,3326,3406,3487,3590,3670,3743,3821,3894,3966,4078,4171,4243,4335,4427,4501,4585,4677,4734,4818,4884,4967,5054,5116,5180,5243,5321,5423,5527,5624,5728,5787,5842", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "278,358,439,522,631,726,824,954,1039,1105,1202,1285,1351,1453,1518,1593,1649,1728,1788,1842,1964,2023,2085,2139,2221,2356,2448,2532,2676,2755,2836,2977,3070,3149,3204,3255,3321,3401,3482,3585,3665,3738,3816,3889,3961,4073,4166,4238,4330,4422,4496,4580,4672,4729,4813,4879,4962,5049,5111,5175,5238,5316,5418,5522,5619,5723,5782,5837,5926"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,3428,4250,4348,4478,4633,4796,4893,4976,5042,5144,5209,5284,5340,5419,5479,5533,5655,5714,5776,5830,5912,6047,6139,6223,6367,6446,6527,6668,6761,6840,6895,6946,7012,7092,7173,7276,7356,7429,7507,7580,7652,7764,7857,7929,8021,8113,8187,8271,8363,8420,8504,8570,8653,8740,8802,8866,8929,9007,9109,9213,9310,9414,9473,9606", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,110", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "328,3150,3231,3314,3423,3518,4343,4473,4558,4694,4888,4971,5037,5139,5204,5279,5335,5414,5474,5528,5650,5709,5771,5825,5907,6042,6134,6218,6362,6441,6522,6663,6756,6835,6890,6941,7007,7087,7168,7271,7351,7424,7502,7575,7647,7759,7852,7924,8016,8108,8182,8266,8358,8415,8499,8565,8648,8735,8797,8861,8924,9002,9104,9208,9305,9409,9468,9523,9690"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ba6274a1892fdd5def28bdd9767110ee\\transformed\\core-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,113", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3523,3621,3723,3822,3924,4028,4132,9924", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3616,3718,3817,3919,4023,4127,4245,10020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\566c99b1f2c65122017c8029b64a207c\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,9837", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,9919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8944fc038c0e2d5da876e70d3ee0f81b\\transformed\\preference-1.2.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,747", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "170,267,345,487,656,742,822"}, "to": {"startLines": "48,50,109,111,114,115,116", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4563,4699,9528,9695,10025,10194,10280", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "4628,4791,9601,9832,10189,10275,10355"}}]}]}