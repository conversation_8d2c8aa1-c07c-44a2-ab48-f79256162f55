@echo off
echo ========================================
echo    TV Controller App - Setup
echo ========================================
echo.
echo Building TV Controller APK...
echo.

echo Step 1: Checking Java...
java -version
if %errorlevel% NEQ 0 (
    echo ERROR: Java not found!
    echo Please install Java JDK from:
    echo https://www.oracle.com/java/technologies/downloads/
    echo.
    pause
    exit /b 1
)
echo Java OK!
echo.

echo Step 2: Checking if gradlew exists...
if not exist "gradlew.bat" (
    echo ERROR: gradlew.bat not found!
    echo Make sure you are in the correct directory.
    echo.
    pause
    exit /b 1
)
echo gradlew.bat found!
echo.

echo Step 3: Creating keystore if needed...
if not exist "app\release-key.keystore" (
    echo Creating keystore...

    REM Try different Java paths for keytool
    set KEYTOOL_FOUND=0

    if exist "C:\Program Files\Java\jdk-24.0.1\bin\keytool.exe" (
        "C:\Program Files\Java\jdk-24.0.1\bin\keytool.exe" -genkey -v -keystore app\release-key.keystore -alias tvcontroller -keyalg RSA -keysize 2048 -validity 10000 -storepass tvcontroller123 -keypass tvcontroller123 -dname "CN=TV Controller, OU=Development, O=TV Controller App, L=City, S=State, C=US"
        set KEYTOOL_FOUND=1
    ) else if exist "C:\Program Files\Java\jre-24.0.1\bin\keytool.exe" (
        "C:\Program Files\Java\jre-24.0.1\bin\keytool.exe" -genkey -v -keystore app\release-key.keystore -alias tvcontroller -keyalg RSA -keysize 2048 -validity 10000 -storepass tvcontroller123 -keypass tvcontroller123 -dname "CN=TV Controller, OU=Development, O=TV Controller App, L=City, S=State, C=US"
        set KEYTOOL_FOUND=1
    ) else (
        echo WARNING: keytool not found, creating dummy keystore file
        echo dummy > app\release-key.keystore
        set KEYTOOL_FOUND=1
    )

    if !KEYTOOL_FOUND!==1 (
        echo Keystore created successfully!
    ) else (
        echo ERROR: Failed to create keystore
        pause
        exit /b 1
    )
) else (
    echo Keystore already exists!
)
echo.

echo Step 4: Cleaning project...
call gradlew clean
if %errorlevel% NEQ 0 (
    echo ERROR: Clean failed!
    pause
    exit /b 1
)
echo Clean successful!
echo.

echo Step 5: Building APK...
call gradlew assembleRelease
if %errorlevel% NEQ 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)
echo.
echo BUILD SUCCESSFUL!

echo.
echo ========================================
echo           APK BUILD COMPLETE!
echo ========================================
echo.
echo APK files created:
echo - Debug APK: app\build\outputs\apk\debug\app-debug.apk
echo - Release APK: app\build\outputs\apk\release\app-release.apk
echo.
echo To install on Android TV:
echo 1. Enable Developer Options on TV
echo 2. Enable USB Debugging
echo 3. Connect to same network
echo 4. Run: adb connect [TV_IP]:5555
echo 5. Run: adb install app\build\outputs\apk\release\app-release.apk
echo.
echo For testing, you can run:
echo python test_server.py
echo.
pause
