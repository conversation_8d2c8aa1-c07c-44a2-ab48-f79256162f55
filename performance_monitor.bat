@echo off
echo ========================================
echo    TV Controller Performance Monitor
echo ========================================
echo.

:MENU
echo اختر العملية:
echo 1. مراقبة استخدام الذاكرة
echo 2. مراقبة استخدام المعالج
echo 3. مراقبة حالة الشبكة
echo 4. عرض سجلات التطبيق
echo 5. اختبار الأداء الشامل
echo 6. إعادة تشغيل التطبيق
echo 7. تنظيف الذاكرة
echo 8. خروج
echo.

set /p choice="أدخل اختيارك (1-8): "

if "%choice%"=="1" goto MEMORY_MONITOR
if "%choice%"=="2" goto CPU_MONITOR
if "%choice%"=="3" goto NETWORK_MONITOR
if "%choice%"=="4" goto LOG_MONITOR
if "%choice%"=="5" goto PERFORMANCE_TEST
if "%choice%"=="6" goto RESTART_APP
if "%choice%"=="7" goto CLEAN_MEMORY
if "%choice%"=="8" goto EXIT

echo اختيار غير صحيح!
goto MENU

:MEMORY_MONITOR
echo.
echo === مراقبة استخدام الذاكرة ===
adb shell dumpsys meminfo com.tvcontroller.app
echo.
pause
goto MENU

:CPU_MONITOR
echo.
echo === مراقبة استخدام المعالج ===
adb shell top -n 1 | findstr com.tvcontroller.app
echo.
pause
goto MENU

:NETWORK_MONITOR
echo.
echo === مراقبة حالة الشبكة ===
adb shell netstat | findstr :8080
echo.
echo === اختبار الاتصال ===
adb shell ping -c 4 192.168.1.100
echo.
pause
goto MENU

:LOG_MONITOR
echo.
echo === سجلات التطبيق (اضغط Ctrl+C للإيقاف) ===
adb logcat | findstr TVController
pause
goto MENU

:PERFORMANCE_TEST
echo.
echo === اختبار الأداء الشامل ===
echo.

echo 1. فحص حالة التطبيق...
adb shell am force-stop com.tvcontroller.app
timeout /t 2 >nul
adb shell am start -n com.tvcontroller.app/.MainActivity

echo 2. فحص استخدام الذاكرة...
adb shell dumpsys meminfo com.tvcontroller.app | findstr "TOTAL"

echo 3. فحص الخدمات النشطة...
adb shell dumpsys activity services | findstr OverlayService

echo 4. اختبار الاتصال بالخادم...
adb shell am broadcast -a com.tvcontroller.app.TEST_CONNECTION

echo 5. اختبار عرض الشاشة السوداء...
adb shell am broadcast -a com.tvcontroller.app.COMMAND --es command "SHOW_BLACK_SCREEN"
timeout /t 3 >nul
adb shell am broadcast -a com.tvcontroller.app.COMMAND --es command "HIDE_BLACK_SCREEN"

echo.
echo اكتمل اختبار الأداء!
pause
goto MENU

:RESTART_APP
echo.
echo === إعادة تشغيل التطبيق ===
adb shell am force-stop com.tvcontroller.app
echo تم إيقاف التطبيق...
timeout /t 2 >nul
adb shell am start -n com.tvcontroller.app/.MainActivity
echo تم تشغيل التطبيق...
echo.
pause
goto MENU

:CLEAN_MEMORY
echo.
echo === تنظيف الذاكرة ===
adb shell am kill-all
adb shell echo 3 > /proc/sys/vm/drop_caches
echo تم تنظيف الذاكرة
echo.
pause
goto MENU

:EXIT
echo.
echo شكراً لاستخدام مراقب الأداء!
exit /b 0
