<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_settings" modulePackage="com.tvcontroller.app" filePath="app\src\main\res\layout\activity_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_settings_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="264" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="62"/></Target><Target id="@+id/et_server_ip" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="55" startOffset="24" endLine="60" endOffset="58"/></Target><Target id="@+id/et_server_port" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="71" startOffset="24" endLine="76" endOffset="49"/></Target><Target id="@+id/et_reconnect_attempts" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="87" startOffset="24" endLine="92" endOffset="47"/></Target><Target id="@+id/btn_test_connection" view="Button"><Expressions/><location startLine="97" startOffset="20" endLine="102" endOffset="75"/></Target><Target id="@+id/switch_auto_start" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="145" startOffset="24" endLine="149" endOffset="52"/></Target><Target id="@+id/rb_arabic" view="RadioButton"><Expressions/><location startLine="167" startOffset="24" endLine="173" endOffset="52"/></Target><Target id="@+id/rb_english" view="RadioButton"><Expressions/><location startLine="175" startOffset="24" endLine="180" endOffset="60"/></Target><Target id="@+id/tv_device_info" view="TextView"><Expressions/><location startLine="210" startOffset="20" endLine="217" endOffset="58"/></Target><Target id="@+id/btn_reset_defaults" view="Button"><Expressions/><location startLine="230" startOffset="16" endLine="237" endOffset="71"/></Target><Target id="@+id/btn_cancel" view="Button"><Expressions/><location startLine="239" startOffset="16" endLine="247" endOffset="73"/></Target><Target id="@+id/btn_save" view="Button"><Expressions/><location startLine="249" startOffset="16" endLine="256" endOffset="70"/></Target></Targets></Layout>