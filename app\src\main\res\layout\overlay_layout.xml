<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/overlay_background"
    android:keepScreenOn="true">

    <!-- Top Progress Bar -->
    <LinearLayout
        android:id="@+id/ll_top_bar"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_alignParentTop="true"
        android:background="@color/overlay_progress"
        android:orientation="horizontal">
        
        <View
            android:id="@+id/progress_indicator"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/red" />
            
    </LinearLayout>

    <!-- Scrolling Text at Top -->
    <TextView
        android:id="@+id/tv_scrolling_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_top_bar"
        android:layout_marginTop="20dp"
        android:text="⚠️ الشاشة مقفلة - Screen Locked - يرجى الانتظار - Please Wait ⚠️"
        android:textColor="@color/overlay_text"
        android:textSize="24sp"
        android:textStyle="bold"
        android:singleLine="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:ellipsize="marquee"
        android:focusable="true"
        android:focusableInTouchMode="true" />

    <!-- Center Message -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:gravity="center">

        <!-- Main Icon -->
        <ImageView
            android:id="@+id/iv_lock_icon"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="30dp"
            android:src="@drawable/ic_lock_screen"
            android:tint="@color/overlay_text" />

        <!-- Main Message -->
        <TextView
            android:id="@+id/tv_overlay_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/overlay_message_default"
            android:textColor="@color/overlay_text"
            android:textSize="32sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="20dp" />

        <!-- Countdown Timer -->
        <TextView
            android:id="@+id/tv_countdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="@color/red"
            android:textSize="28sp"
            android:textStyle="bold"
            android:gravity="center"
            android:visibility="gone" />

        <!-- Blinking Warning -->
        <TextView
            android:id="@+id/tv_warning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="⚠️ تحذير - WARNING ⚠️"
            android:textColor="@color/yellow"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginTop="30dp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Bottom Status Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal"
        android:background="@color/semi_transparent"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Connection Status -->
        <TextView
            android:id="@+id/tv_connection_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="متصل - Connected"
            android:textColor="@color/status_connected"
            android:textSize="16sp"
            android:gravity="start" />

        <!-- Time Display -->
        <TextView
            android:id="@+id/tv_current_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textColor="@color/overlay_text"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- Server Info -->
        <TextView
            android:id="@+id/tv_server_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Server: 192.168.1.100"
            android:textColor="@color/overlay_text"
            android:textSize="14sp"
            android:gravity="end" />

    </LinearLayout>

    <!-- Animated Background Elements -->
    <View
        android:id="@+id/animated_circle_1"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="100dp"
        android:layout_marginEnd="100dp"
        android:background="@drawable/animated_circle"
        android:alpha="0.3" />

    <View
        android:id="@+id/animated_circle_2"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentStart="true"
        android:layout_marginBottom="150dp"
        android:layout_marginStart="150dp"
        android:background="@drawable/animated_circle"
        android:alpha="0.2" />

</RelativeLayout>
