package com.minimal.tvapp;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.IBinder;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;

public class OverlayService extends Service {
    
    private WindowManager windowManager;
    private View overlayView;
    private static final int NOTIFICATION_ID = 1;
    private static final String CHANNEL_ID = "OverlayServiceChannel";
    
    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
        windowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        startForeground(NOTIFICATION_ID, createNotification());
        showOverlay();
        return START_STICKY;
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "Overlay Service Channel",
                NotificationManager.IMPORTANCE_LOW
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(channel);
        }
    }
    
    private Notification createNotification() {
        Notification.Builder builder;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder = new Notification.Builder(this, CHANNEL_ID);
        } else {
            builder = new Notification.Builder(this);
        }
        
        return builder
            .setContentTitle("TV Controller")
            .setContentText("Overlay service is running")
            .setSmallIcon(android.R.drawable.ic_media_play)
            .build();
    }
    
    private void showOverlay() {
        if (overlayView != null) return;
        
        WindowManager.LayoutParams params = new WindowManager.LayoutParams();
        params.width = WindowManager.LayoutParams.WRAP_CONTENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            params.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        
        params.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                      WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL;
        params.format = PixelFormat.TRANSLUCENT;
        params.gravity = Gravity.TOP | Gravity.START;
        params.x = 100;
        params.y = 100;
        
        overlayView = createOverlayView();
        windowManager.addView(overlayView, params);
    }
    
    private View createOverlayView() {
        LinearLayout layout = new LinearLayout(this);
        layout.setOrientation(LinearLayout.VERTICAL);
        layout.setBackgroundColor(Color.argb(200, 0, 0, 0));
        layout.setPadding(20, 20, 20, 20);
        
        Button upButton = new Button(this);
        upButton.setText("UP");
        upButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Send UP command
            }
        });
        layout.addView(upButton);
        
        Button downButton = new Button(this);
        downButton.setText("DOWN");
        downButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Send DOWN command
            }
        });
        layout.addView(downButton);
        
        Button closeButton = new Button(this);
        closeButton.setText("CLOSE");
        closeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                hideOverlay();
                stopSelf();
            }
        });
        layout.addView(closeButton);
        
        return layout;
    }
    
    private void hideOverlay() {
        if (overlayView != null) {
            windowManager.removeView(overlayView);
            overlayView = null;
        }
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        hideOverlay();
    }
}
